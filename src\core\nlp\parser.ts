/**
 * Natural Language Processing Engine for ArchitekAI
 * 
 * Parses natural language descriptions and extracts architecture components and relationships.
 */

import natural from 'natural';
import compromise from 'compromise';
import { 
  ParsedDescription, 
  ExtractedComponent, 
  ExtractedConnection, 
  ArchitecturePattern, 
  ComponentType, 
  ConnectionType,
  NLPEntity,
  Architecture,
  Component,
  Connection
} from '@/types/architecture';
import { NLPConfig } from '@/types/config';
import { createLogger } from '@/utils/logger';

const logger = createLogger('NLPEngine');

export class NLPEngine {
  private config: NLPConfig;
  private stemmer: any;
  private tokenizer: any;
  private componentPatterns: Map<ComponentType, RegExp[]>;
  private connectionPatterns: Map<ConnectionType, RegExp[]>;
  private architecturePatterns: Map<ArchitecturePattern, RegExp[]>;

  constructor(config: NLPConfig) {
    this.config = config;
    this.stemmer = natural.PorterStemmer;
    this.tokenizer = new natural.WordTokenizer();
    
    this.initializePatterns();
  }

  /**
   * Parse a natural language description into architecture components
   */
  async parse(description: string): Promise<ParsedDescription> {
    const startTime = Date.now();
    
    try {
      logger.debug(`Parsing description: "${description}"`);
      
      // Preprocess the text
      const preprocessedText = this.preprocessText(description);
      
      // Extract entities using compromise
      const doc = compromise(preprocessedText);
      const entities = this.extractEntities(doc);
      
      // Extract components
      const extractedComponents = this.extractComponents(preprocessedText, entities);
      
      // Extract connections
      const extractedConnections = this.extractConnections(preprocessedText, extractedComponents);
      
      // Determine architecture pattern
      const suggestedPattern = this.determineArchitecturePattern(preprocessedText, extractedComponents);
      
      // Calculate confidence score
      const confidence = this.calculateConfidence(extractedComponents, extractedConnections, suggestedPattern);
      
      // Create architecture object
      const architecture = this.createArchitecture(description, extractedComponents, extractedConnections, suggestedPattern);
      
      const processingTime = Date.now() - startTime;
      
      const result: ParsedDescription = {
        originalText: description,
        extractedComponents,
        extractedConnections,
        suggestedPattern,
        confidence,
        metadata: {
          processingTime,
          nlpVersion: '1.0.0',
          patterns: this.getMatchedPatterns(preprocessedText),
          keywords: this.extractKeywords(preprocessedText),
          entities,
        },
        architecture,
      };
      
      logger.debug(`Parsing completed in ${processingTime}ms with confidence ${confidence}`);
      
      return result;
      
    } catch (error) {
      logger.error('Failed to parse description:', error);
      throw new Error(`NLP parsing failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Preprocess text for better analysis
   */
  private preprocessText(text: string): string {
    let processed = text.toLowerCase().trim();
    
    if (this.config.preprocessing.normalize) {
      // Normalize whitespace and punctuation
      processed = processed.replace(/\s+/g, ' ');
      processed = processed.replace(/[^\w\s-]/g, ' ');
    }
    
    if (this.config.preprocessing.removeStopWords) {
      const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
      const words = processed.split(' ');
      processed = words.filter(word => !stopWords.has(word)).join(' ');
    }
    
    return processed;
  }

  /**
   * Extract named entities from text
   */
  private extractEntities(doc: any): NLPEntity[] {
    const entities: NLPEntity[] = [];
    
    // Extract organizations (potential service names)
    const orgs = doc.organizations().out('array');
    orgs.forEach((org: string) => {
      entities.push({
        text: org,
        type: 'organization',
        confidence: 0.8,
        start: 0,
        end: org.length,
      });
    });
    
    // Extract nouns (potential components)
    const nouns = doc.nouns().out('array');
    nouns.forEach((noun: string) => {
      entities.push({
        text: noun,
        type: 'noun',
        confidence: 0.6,
        start: 0,
        end: noun.length,
      });
    });
    
    return entities;
  }

  /**
   * Extract components from preprocessed text
   */
  private extractComponents(text: string, entities: NLPEntity[]): ExtractedComponent[] {
    const components: ExtractedComponent[] = [];
    const foundComponents = new Set<string>();
    
    // Check against component patterns
    for (const [componentType, patterns] of this.componentPatterns) {
      for (const pattern of patterns) {
        const matches = text.match(pattern);
        if (matches) {
          matches.forEach(match => {
            const componentName = this.generateComponentName(match, componentType);
            
            if (!foundComponents.has(componentName.toLowerCase())) {
              foundComponents.add(componentName.toLowerCase());
              
              components.push({
                name: componentName,
                type: componentType,
                confidence: this.calculateComponentConfidence(match, componentType),
                context: this.getContext(text, match),
                properties: this.extractComponentProperties(match, componentType),
              });
            }
          });
        }
      }
    }
    
    // Extract components from entities
    entities.forEach(entity => {
      if (entity.type === 'noun' && entity.confidence > 0.7) {
        const componentType = this.inferComponentType(entity.text);
        if (componentType && !foundComponents.has(entity.text.toLowerCase())) {
          foundComponents.add(entity.text.toLowerCase());
          
          components.push({
            name: this.capitalizeWords(entity.text),
            type: componentType,
            confidence: entity.confidence * 0.8, // Slightly lower confidence for inferred types
            context: [entity.text],
            properties: {},
          });
        }
      }
    });
    
    return components;
  }

  /**
   * Extract connections between components
   */
  private extractConnections(text: string, components: ExtractedComponent[]): ExtractedConnection[] {
    const connections: ExtractedConnection[] = [];
    
    // Look for connection patterns
    for (const [connectionType, patterns] of this.connectionPatterns) {
      for (const pattern of patterns) {
        const matches = text.match(pattern);
        if (matches) {
          // Try to find source and target components
          const sourceComponent = this.findNearestComponent(text, matches[0]!, components, 'before');
          const targetComponent = this.findNearestComponent(text, matches[0]!, components, 'after');
          
          if (sourceComponent && targetComponent && sourceComponent !== targetComponent) {
            connections.push({
              source: sourceComponent.name,
              target: targetComponent.name,
              type: connectionType,
              confidence: this.calculateConnectionConfidence(matches[0]!, connectionType),
              context: this.getContext(text, matches[0]!),
              properties: {},
            });
          }
        }
      }
    }
    
    // Infer implicit connections
    const implicitConnections = this.inferImplicitConnections(components);
    connections.push(...implicitConnections);
    
    return connections;
  }

  /**
   * Determine the most likely architecture pattern
   */
  private determineArchitecturePattern(text: string, components: ExtractedComponent[]): ArchitecturePattern {
    const patternScores = new Map<ArchitecturePattern, number>();
    
    // Check against architecture patterns
    for (const [pattern, regexes] of this.architecturePatterns) {
      let score = 0;
      
      for (const regex of regexes) {
        const matches = text.match(regex);
        if (matches) {
          score += matches.length;
        }
      }
      
      patternScores.set(pattern, score);
    }
    
    // Consider component types for pattern inference
    const componentTypes = components.map(c => c.type);
    
    if (componentTypes.includes(ComponentType.API_GATEWAY) && componentTypes.length > 3) {
      patternScores.set(ArchitecturePattern.MICROSERVICES, (patternScores.get(ArchitecturePattern.MICROSERVICES) || 0) + 2);
    }
    
    if (componentTypes.includes(ComponentType.FUNCTION)) {
      patternScores.set(ArchitecturePattern.SERVERLESS, (patternScores.get(ArchitecturePattern.SERVERLESS) || 0) + 2);
    }
    
    if (componentTypes.includes(ComponentType.QUEUE) || componentTypes.includes(ComponentType.EVENT_STREAM)) {
      patternScores.set(ArchitecturePattern.EVENT_DRIVEN, (patternScores.get(ArchitecturePattern.EVENT_DRIVEN) || 0) + 1);
    }
    
    // Return pattern with highest score
    let bestPattern = ArchitecturePattern.MONOLITHIC;
    let bestScore = 0;
    
    for (const [pattern, score] of patternScores) {
      if (score > bestScore) {
        bestScore = score;
        bestPattern = pattern;
      }
    }
    
    return bestPattern;
  }

  /**
   * Calculate overall confidence score
   */
  private calculateConfidence(
    components: ExtractedComponent[], 
    connections: ExtractedConnection[], 
    pattern: ArchitecturePattern
  ): number {
    if (components.length === 0) return 0;
    
    const componentConfidence = components.reduce((sum, c) => sum + c.confidence, 0) / components.length;
    const connectionConfidence = connections.length > 0 
      ? connections.reduce((sum, c) => sum + c.confidence, 0) / connections.length 
      : 0.5;
    
    const patternConfidence = this.getPatternConfidence(pattern, components);
    
    // Weighted average
    return (componentConfidence * 0.5 + connectionConfidence * 0.3 + patternConfidence * 0.2);
  }

  /**
   * Create architecture object from extracted data
   */
  private createArchitecture(
    description: string,
    extractedComponents: ExtractedComponent[],
    extractedConnections: ExtractedConnection[],
    pattern: ArchitecturePattern
  ): Architecture {
    const components: Component[] = extractedComponents.map((ec, index) => ({
      id: `comp-${index + 1}`,
      name: ec.name,
      type: ec.type,
      description: `${ec.name} component`,
      properties: ec.properties,
      position: {
        x: 100 + (index % 4) * 200,
        y: 100 + Math.floor(index / 4) * 150,
      },
      metadata: {
        tags: ec.context,
        category: this.getComponentCategory(ec.type),
        confidence: ec.confidence,
      },
    }));

    const connections: Connection[] = extractedConnections.map((ec, index) => {
      const sourceComp = components.find(c => c.name === ec.source);
      const targetComp = components.find(c => c.name === ec.target);
      
      return {
        id: `conn-${index + 1}`,
        source: sourceComp?.id || 'unknown',
        target: targetComp?.id || 'unknown',
        type: ec.type,
        label: this.generateConnectionLabel(ec.type),
        properties: ec.properties,
      };
    });

    return {
      id: `arch-${Date.now()}`,
      name: this.generateArchitectureName(description),
      description,
      components,
      connections,
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        version: '1.0.0',
        template: pattern,
      },
    };
  }

  /**
   * Initialize pattern matching rules
   */
  private initializePatterns(): void {
    this.componentPatterns = new Map([
      [ComponentType.API_GATEWAY, [/api\s+gateway/gi, /gateway/gi, /api\s+proxy/gi]],
      [ComponentType.SERVICE, [/service/gi, /microservice/gi, /api/gi]],
      [ComponentType.DATABASE, [/database/gi, /db/gi, /storage/gi, /data\s+store/gi]],
      [ComponentType.LOAD_BALANCER, [/load\s+balancer/gi, /balancer/gi, /lb/gi]],
      [ComponentType.CACHE, [/cache/gi, /redis/gi, /memcached/gi]],
      [ComponentType.QUEUE, [/queue/gi, /message\s+queue/gi, /mq/gi, /rabbitmq/gi]],
      [ComponentType.FUNCTION, [/function/gi, /lambda/gi, /serverless/gi]],
      [ComponentType.CDN, [/cdn/gi, /content\s+delivery/gi]],
      [ComponentType.WEB_APP, [/web\s+app/gi, /frontend/gi, /ui/gi, /client/gi]],
      [ComponentType.MOBILE_APP, [/mobile\s+app/gi, /mobile/gi, /ios/gi, /android/gi]],
    ]);

    this.connectionPatterns = new Map([
      [ConnectionType.HTTP, [/http/gi, /rest/gi, /api\s+call/gi]],
      [ConnectionType.HTTPS, [/https/gi, /secure\s+http/gi]],
      [ConnectionType.DATABASE_CONNECTION, [/connects?\s+to/gi, /queries/gi, /stores?\s+in/gi]],
      [ConnectionType.MESSAGE_QUEUE, [/sends?\s+message/gi, /publishes/gi, /subscribes/gi]],
      [ConnectionType.API_CALL, [/calls/gi, /invokes/gi, /requests/gi]],
    ]);

    this.architecturePatterns = new Map([
      [ArchitecturePattern.MICROSERVICES, [/microservice/gi, /distributed/gi, /service\s+mesh/gi]],
      [ArchitecturePattern.SERVERLESS, [/serverless/gi, /lambda/gi, /function/gi, /faas/gi]],
      [ArchitecturePattern.MONOLITHIC, [/monolith/gi, /single\s+application/gi]],
      [ArchitecturePattern.EVENT_DRIVEN, [/event/gi, /message/gi, /publish/gi, /subscribe/gi]],
      [ArchitecturePattern.LAYERED, [/layer/gi, /tier/gi, /n-tier/gi]],
    ]);
  }

  // Helper methods (simplified implementations)
  private generateComponentName(match: string, type: ComponentType): string {
    return this.capitalizeWords(match.replace(/\b(service|api|database|db)\b/gi, '').trim() || type.replace('_', ' '));
  }

  private calculateComponentConfidence(match: string, type: ComponentType): number {
    return Math.min(0.9, 0.6 + (match.length / 20));
  }

  private getContext(text: string, match: string): string[] {
    const words = text.split(' ');
    const matchIndex = words.findIndex(word => word.includes(match.split(' ')[0]!));
    const start = Math.max(0, matchIndex - 2);
    const end = Math.min(words.length, matchIndex + 3);
    return words.slice(start, end);
  }

  private extractComponentProperties(match: string, type: ComponentType): Record<string, unknown> {
    return { originalMatch: match, inferredType: type };
  }

  private inferComponentType(text: string): ComponentType | null {
    const lowerText = text.toLowerCase();
    if (lowerText.includes('service')) return ComponentType.SERVICE;
    if (lowerText.includes('database') || lowerText.includes('db')) return ComponentType.DATABASE;
    if (lowerText.includes('api')) return ComponentType.API_GATEWAY;
    if (lowerText.includes('queue')) return ComponentType.QUEUE;
    if (lowerText.includes('cache')) return ComponentType.CACHE;
    return null;
  }

  private capitalizeWords(text: string): string {
    return text.replace(/\b\w/g, l => l.toUpperCase());
  }

  private findNearestComponent(text: string, match: string, components: ExtractedComponent[], direction: 'before' | 'after'): ExtractedComponent | null {
    // Simplified implementation - would need more sophisticated logic
    return components[0] || null;
  }

  private calculateConnectionConfidence(match: string, type: ConnectionType): number {
    return Math.min(0.8, 0.5 + (match.length / 30));
  }

  private inferImplicitConnections(components: ExtractedComponent[]): ExtractedConnection[] {
    const connections: ExtractedConnection[] = [];
    
    // Simple rule: services connect to databases
    const services = components.filter(c => c.type === ComponentType.SERVICE);
    const databases = components.filter(c => c.type === ComponentType.DATABASE);
    
    services.forEach(service => {
      databases.forEach(db => {
        connections.push({
          source: service.name,
          target: db.name,
          type: ConnectionType.DATABASE_CONNECTION,
          confidence: 0.6,
          context: ['inferred'],
          properties: { inferred: true },
        });
      });
    });
    
    return connections;
  }

  private getPatternConfidence(pattern: ArchitecturePattern, components: ExtractedComponent[]): number {
    // Simplified pattern confidence calculation
    const componentTypes = components.map(c => c.type);
    
    switch (pattern) {
      case ArchitecturePattern.MICROSERVICES:
        return componentTypes.includes(ComponentType.API_GATEWAY) && components.length > 2 ? 0.8 : 0.4;
      case ArchitecturePattern.SERVERLESS:
        return componentTypes.includes(ComponentType.FUNCTION) ? 0.9 : 0.3;
      case ArchitecturePattern.MONOLITHIC:
        return components.length <= 3 ? 0.7 : 0.3;
      default:
        return 0.5;
    }
  }

  private getMatchedPatterns(text: string): string[] {
    const patterns: string[] = [];
    
    for (const [pattern, regexes] of this.architecturePatterns) {
      for (const regex of regexes) {
        if (regex.test(text)) {
          patterns.push(pattern);
          break;
        }
      }
    }
    
    return patterns;
  }

  private extractKeywords(text: string): string[] {
    const tokens = this.tokenizer.tokenize(text);
    return tokens.filter((token: string) => token.length > 3);
  }

  private getComponentCategory(type: ComponentType): string {
    const categories: Record<ComponentType, string> = {
      [ComponentType.SERVICE]: 'compute',
      [ComponentType.DATABASE]: 'storage',
      [ComponentType.API_GATEWAY]: 'networking',
      [ComponentType.LOAD_BALANCER]: 'networking',
      [ComponentType.CACHE]: 'storage',
      [ComponentType.QUEUE]: 'messaging',
      [ComponentType.FUNCTION]: 'compute',
      [ComponentType.CDN]: 'networking',
      [ComponentType.WEB_APP]: 'presentation',
      [ComponentType.MOBILE_APP]: 'presentation',
      [ComponentType.STORAGE]: 'storage',
      [ComponentType.CONTAINER]: 'compute',
      [ComponentType.EXTERNAL_SERVICE]: 'external',
      [ComponentType.USER_INTERFACE]: 'presentation',
      [ComponentType.DESKTOP_APP]: 'presentation',
      [ComponentType.IOT_DEVICE]: 'edge',
      [ComponentType.NETWORK]: 'networking',
      [ComponentType.SECURITY]: 'security',
      [ComponentType.MONITORING]: 'observability',
      [ComponentType.LOGGING]: 'observability',
      [ComponentType.ANALYTICS]: 'analytics',
      [ComponentType.NOTIFICATION]: 'messaging',
      [ComponentType.WORKFLOW]: 'orchestration',
      [ComponentType.DATA_PIPELINE]: 'data',
      [ComponentType.ML_MODEL]: 'ai',
      [ComponentType.CUSTOM]: 'custom',
    };
    
    return categories[type] || 'unknown';
  }

  private generateConnectionLabel(type: ConnectionType): string {
    const labels: Record<ConnectionType, string> = {
      [ConnectionType.HTTP]: 'HTTP Request',
      [ConnectionType.HTTPS]: 'HTTPS Request',
      [ConnectionType.DATABASE_CONNECTION]: 'Data Access',
      [ConnectionType.MESSAGE_QUEUE]: 'Message',
      [ConnectionType.API_CALL]: 'API Call',
      [ConnectionType.TCP]: 'TCP Connection',
      [ConnectionType.UDP]: 'UDP Connection',
      [ConnectionType.WEBSOCKET]: 'WebSocket',
      [ConnectionType.GRPC]: 'gRPC Call',
      [ConnectionType.GRAPHQL]: 'GraphQL Query',
      [ConnectionType.REST]: 'REST API',
      [ConnectionType.SOAP]: 'SOAP Call',
      [ConnectionType.EVENT_STREAM]: 'Event Stream',
      [ConnectionType.FILE_SYSTEM]: 'File Access',
      [ConnectionType.NETWORK]: 'Network',
      [ConnectionType.VPN]: 'VPN Connection',
      [ConnectionType.DEPENDENCY]: 'Dependency',
      [ConnectionType.DATA_FLOW]: 'Data Flow',
      [ConnectionType.CONTROL_FLOW]: 'Control Flow',
      [ConnectionType.INHERITANCE]: 'Inherits',
      [ConnectionType.COMPOSITION]: 'Composed of',
      [ConnectionType.AGGREGATION]: 'Aggregates',
      [ConnectionType.ASSOCIATION]: 'Associated with',
      [ConnectionType.CUSTOM]: 'Custom Connection',
    };
    
    return labels[type] || 'Connection';
  }

  private generateArchitectureName(description: string): string {
    const words = description.split(' ').slice(0, 3);
    return this.capitalizeWords(words.join(' ')) + ' Architecture';
  }
}

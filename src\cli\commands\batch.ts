/**
 * Batch Command for ArchitekAI CLI
 * 
 * Handles batch processing of multiple architecture descriptions.
 */

import { Command } from 'commander';
import chalk from 'chalk';
import path from 'path';
import { glob } from 'glob';
import { ConfigManager } from '@/core/config/manager';
import { OutputFormat } from '@/types/architecture';
import { createLogger } from '@/utils/logger';
import { 
  createSpinner, 
  createProgressBar,
  displaySuccess, 
  displayError, 
  displayInfo,
  displayWarning,
  ensureOutputPath,
  formatDuration,
  formatFileSize,
  confirmAction,
} from '../utils';

const logger = createLogger('BatchCommand');

interface BatchOptions {
  input: string;
  output?: string;
  format?: OutputFormat;
  template?: string;
  parallel?: number;
  validate?: boolean;
  overwrite?: boolean;
  verbose?: boolean;
  dryRun?: boolean;
  filter?: string;
  exclude?: string;
}

export function batchCommand(configManager: ConfigManager): Command {
  const command = new Command('batch');

  command
    .description('Process multiple architecture descriptions in batch')
    .requiredOption('-i, --input <pattern>', 'input file pattern (supports glob patterns)')
    .option('-o, --output <directory>', 'output directory')
    .option('-f, --format <format>', 'output format for all diagrams', 'mermaid')
    .option('-t, --template <name>', 'apply template to all descriptions')
    .option('-p, --parallel <number>', 'number of parallel processes', '4')
    .option('--validate', 'validate all generated diagrams')
    .option('--overwrite', 'overwrite existing files without confirmation')
    .option('--verbose', 'enable verbose output')
    .option('--dry-run', 'show what would be processed without generating')
    .option('--filter <pattern>', 'filter files by name pattern')
    .option('--exclude <pattern>', 'exclude files by name pattern')
    .addHelpText('after', `
Examples:
  ${chalk.cyan('architekAI batch --input "./descriptions/*.txt"')}
  ${chalk.cyan('architekAI batch --input "./docs/**/*.md" --format plantuml --output ./diagrams')}
  ${chalk.cyan('architekAI batch --input "./specs/*.yaml" --template microservices --parallel 8')}
  ${chalk.cyan('architekAI batch --input "./examples/*.txt" --dry-run')}
    `)
    .action(async (options: BatchOptions) => {
      await executeBatch(options, configManager);
    });

  return command;
}

async function executeBatch(options: BatchOptions, configManager: ConfigManager): Promise<void> {
  const startTime = Date.now();
  let spinner = createSpinner('Initializing batch processing...');
  
  try {
    spinner.start();

    // Get configuration
    const config = configManager.getConfig();
    
    // Merge options with config defaults
    const mergedOptions = {
      format: (options.format || config.output.defaultFormat) as OutputFormat,
      parallel: parseInt(String(options.parallel || 4)),
      validate: options.validate ?? true,
      verbose: options.verbose ?? config.cli.verbose,
      ...options,
    };

    // Find input files
    spinner.text = 'Finding input files...';
    const inputFiles = await findInputFiles(mergedOptions.input, {
      filter: options.filter,
      exclude: options.exclude,
    });

    if (inputFiles.length === 0) {
      spinner.fail('No input files found');
      displayError(`No files found matching pattern: ${mergedOptions.input}`);
      process.exit(1);
    }

    spinner.stop();
    displayInfo(`Found ${inputFiles.length} file(s) to process`);

    if (mergedOptions.verbose) {
      console.log(chalk.gray('Input files:'));
      inputFiles.forEach(file => {
        console.log(chalk.gray(`  • ${file}`));
      });
    }

    // Dry run mode
    if (mergedOptions.dryRun) {
      displayInfo('Dry run mode - showing what would be processed:');
      
      const processingSummary = await analyzeBatchJob(inputFiles, mergedOptions);
      displayBatchSummary(processingSummary);
      
      displayInfo('Use --dry-run=false to execute the batch processing');
      return;
    }

    // Confirm processing
    if (!mergedOptions.overwrite && inputFiles.length > 10) {
      const shouldContinue = await confirmAction(
        `Process ${inputFiles.length} files? This may take some time.`, 
        true
      );
      
      if (!shouldContinue) {
        displayInfo('Batch processing cancelled.');
        return;
      }
    }

    // Process files
    spinner = createSpinner('Processing files...');
    spinner.start();

    const results = await processBatchFiles(inputFiles, mergedOptions, configManager);

    const endTime = Date.now();
    const totalDuration = endTime - startTime;

    spinner.succeed('Batch processing completed!');

    // Display results summary
    displayBatchResults(results, totalDuration);

  } catch (error) {
    if (spinner) {
      spinner.fail('Batch processing failed');
    }
    
    logger.error('Batch command failed:', error);
    displayError(error instanceof Error ? error.message : 'Unknown error occurred');
    process.exit(1);
  }
}

async function findInputFiles(pattern: string, filters: { filter?: string; exclude?: string }): Promise<string[]> {
  try {
    let files = await glob(pattern, {
      ignore: ['node_modules/**', '.git/**', 'dist/**', 'build/**'],
    });

    // Apply additional filters
    if (filters.filter) {
      const filterRegex = new RegExp(filters.filter, 'i');
      files = files.filter(file => filterRegex.test(path.basename(file)));
    }

    if (filters.exclude) {
      const excludeRegex = new RegExp(filters.exclude, 'i');
      files = files.filter(file => !excludeRegex.test(path.basename(file)));
    }

    return files.sort();
  } catch (error) {
    throw new Error(`Failed to find input files: ${error instanceof Error ? error.message : String(error)}`);
  }
}

async function analyzeBatchJob(files: string[], options: BatchOptions): Promise<BatchAnalysis> {
  const fs = await import('fs-extra');
  
  let totalSize = 0;
  const fileAnalysis: FileAnalysis[] = [];

  for (const file of files) {
    try {
      const stats = await fs.stat(file);
      const content = await fs.readFile(file, 'utf8');
      
      const analysis: FileAnalysis = {
        path: file,
        size: stats.size,
        lines: content.split('\n').length,
        estimatedProcessingTime: estimateProcessingTime(content),
        outputPath: generateOutputPath(file, options),
      };

      fileAnalysis.push(analysis);
      totalSize += stats.size;
    } catch (error) {
      logger.warn(`Failed to analyze file ${file}:`, error);
    }
  }

  return {
    totalFiles: files.length,
    totalSize,
    estimatedDuration: fileAnalysis.reduce((sum, f) => sum + f.estimatedProcessingTime, 0),
    files: fileAnalysis,
  };
}

async function processBatchFiles(
  files: string[], 
  options: BatchOptions, 
  configManager: ConfigManager
): Promise<BatchResults> {
  const results: BatchResults = {
    processed: 0,
    successful: 0,
    failed: 0,
    skipped: 0,
    errors: [],
    outputs: [],
    totalSize: 0,
  };

  const progressBar = createProgressBar(files.length);
  const parallelLimit = Math.min(options.parallel || 4, files.length);
  
  // Process files in parallel batches
  for (let i = 0; i < files.length; i += parallelLimit) {
    const batch = files.slice(i, i + parallelLimit);
    const batchPromises = batch.map(file => processFile(file, options, configManager));
    
    const batchResults = await Promise.allSettled(batchPromises);
    
    batchResults.forEach((result, index) => {
      const file = batch[index]!;
      results.processed++;
      
      if (result.status === 'fulfilled') {
        results.successful++;
        results.outputs.push(result.value);
        results.totalSize += result.value.size;
      } else {
        results.failed++;
        results.errors.push({
          file,
          error: result.reason instanceof Error ? result.reason.message : String(result.reason),
        });
      }
      
      progressBar.update(results.processed, `Processing ${path.basename(file)}`);
    });
  }

  progressBar.complete(`Processed ${results.processed} files`);
  
  return results;
}

async function processFile(file: string, options: BatchOptions, configManager: ConfigManager): Promise<FileOutput> {
  const fs = await import('fs-extra');
  
  try {
    // Read input file
    const content = await fs.readFile(file, 'utf8');
    
    // Extract description from file content
    const description = extractDescription(content, file);
    
    if (!description.trim()) {
      throw new Error('No valid description found in file');
    }

    // TODO: Replace with actual processing when NLP and generators are implemented
    const mockResult = await processMockDescription(description, options);
    
    // Generate output path
    const outputPath = generateOutputPath(file, options);
    await ensureOutputPath(outputPath);
    
    // Save result
    await fs.writeFile(outputPath, mockResult.content, 'utf8');
    
    return {
      inputFile: file,
      outputFile: outputPath,
      size: Buffer.byteLength(mockResult.content, 'utf8'),
      processingTime: mockResult.processingTime,
      componentCount: mockResult.componentCount,
      warnings: mockResult.warnings,
    };
    
  } catch (error) {
    throw new Error(`Failed to process ${file}: ${error instanceof Error ? error.message : String(error)}`);
  }
}

function extractDescription(content: string, filePath: string): string {
  const ext = path.extname(filePath).toLowerCase();
  
  // Handle different file types
  switch (ext) {
    case '.md':
      // Extract from markdown - look for architecture sections
      const mdMatch = content.match(/(?:^|\n)#+\s*(?:architecture|system|design)[\s\S]*?\n([\s\S]*?)(?=\n#+|\n---|\n```|$)/i);
      return mdMatch ? mdMatch[1]!.trim() : content.trim();
      
    case '.yaml':
    case '.yml':
      // Extract description field from YAML
      try {
        const yaml = require('yaml');
        const parsed = yaml.parse(content);
        return parsed.description || parsed.architecture || content.trim();
      } catch {
        return content.trim();
      }
      
    case '.json':
      // Extract description field from JSON
      try {
        const parsed = JSON.parse(content);
        return parsed.description || parsed.architecture || content.trim();
      } catch {
        return content.trim();
      }
      
    default:
      // Plain text files
      return content.trim();
  }
}

async function processMockDescription(description: string, options: BatchOptions): Promise<MockProcessingResult> {
  // Mock processing - replace with actual implementation
  const processingTime = Math.random() * 1000 + 500; // 500-1500ms
  const componentCount = Math.floor(Math.random() * 10) + 3; // 3-12 components
  
  await new Promise(resolve => setTimeout(resolve, processingTime));
  
  const content = `// Generated from: ${description.substring(0, 50)}...
// Format: ${options.format}
// Components: ${componentCount}

graph TD
    A[Component A] --> B[Component B]
    B --> C[Component C]
    C --> D[Component D]
`;

  return {
    content,
    processingTime,
    componentCount,
    warnings: [],
  };
}

function generateOutputPath(inputFile: string, options: BatchOptions): string {
  const inputDir = path.dirname(inputFile);
  const inputName = path.basename(inputFile, path.extname(inputFile));
  const extension = getFileExtension(options.format || 'mermaid');
  
  const outputDir = options.output || path.join(inputDir, 'diagrams');
  const outputName = `${inputName}.${extension}`;
  
  return path.join(outputDir, outputName);
}

function getFileExtension(format: string): string {
  const extensions: Record<string, string> = {
    mermaid: 'mmd',
    plantuml: 'puml',
    ascii: 'txt',
    drawio: 'drawio',
    lucidchart: 'json',
  };
  return extensions[format] || 'txt';
}

function estimateProcessingTime(content: string): number {
  // Rough estimation based on content length
  const baseTime = 1000; // 1 second base
  const lengthFactor = content.length / 1000; // 1ms per character (roughly)
  return baseTime + lengthFactor;
}

function displayBatchSummary(analysis: BatchAnalysis): void {
  console.log(chalk.cyan('\n📊 Batch Processing Analysis:'));
  console.log(chalk.gray('─'.repeat(40)));
  
  console.log(`${chalk.bold('Total Files:')} ${analysis.totalFiles}`);
  console.log(`${chalk.bold('Total Size:')} ${formatFileSize(analysis.totalSize)}`);
  console.log(`${chalk.bold('Estimated Duration:')} ${formatDuration(analysis.estimatedDuration)}`);
  
  if (analysis.files.length <= 10) {
    console.log(`\n${chalk.bold('Files to process:')}`);
    analysis.files.forEach(file => {
      console.log(`  • ${chalk.cyan(path.basename(file.path))} (${formatFileSize(file.size)}, ${file.lines} lines)`);
    });
  }
}

function displayBatchResults(results: BatchResults, totalDuration: number): void {
  console.log(chalk.cyan('\n📈 Batch Processing Results:'));
  console.log(chalk.gray('─'.repeat(40)));
  
  console.log(`${chalk.bold('Total Processed:')} ${results.processed}`);
  console.log(`${chalk.green('✅ Successful:')} ${results.successful}`);
  console.log(`${chalk.red('❌ Failed:')} ${results.failed}`);
  console.log(`${chalk.yellow('⏭️  Skipped:')} ${results.skipped}`);
  console.log(`${chalk.bold('Total Output Size:')} ${formatFileSize(results.totalSize)}`);
  console.log(`${chalk.bold('Total Duration:')} ${formatDuration(totalDuration)}`);
  
  if (results.errors.length > 0) {
    console.log(chalk.red('\n❌ Errors:'));
    results.errors.forEach(error => {
      console.log(chalk.red(`  • ${path.basename(error.file)}: ${error.error}`));
    });
  }
  
  if (results.successful > 0) {
    console.log(chalk.green(`\n✅ Successfully generated ${results.successful} diagram(s)`));
  }
}

// Type definitions
interface BatchAnalysis {
  totalFiles: number;
  totalSize: number;
  estimatedDuration: number;
  files: FileAnalysis[];
}

interface FileAnalysis {
  path: string;
  size: number;
  lines: number;
  estimatedProcessingTime: number;
  outputPath: string;
}

interface BatchResults {
  processed: number;
  successful: number;
  failed: number;
  skipped: number;
  errors: Array<{ file: string; error: string }>;
  outputs: FileOutput[];
  totalSize: number;
}

interface FileOutput {
  inputFile: string;
  outputFile: string;
  size: number;
  processingTime: number;
  componentCount: number;
  warnings: string[];
}

interface MockProcessingResult {
  content: string;
  processingTime: number;
  componentCount: number;
  warnings: string[];
}

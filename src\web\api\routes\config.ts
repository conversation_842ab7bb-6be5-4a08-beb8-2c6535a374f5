/**
 * Configuration API Routes for ArchitekAI
 * 
 * Handles configuration management via REST API.
 */

import { Router, Request, Response, NextFunction } from 'express';
import { ConfigManager } from '@/core/config/manager';
import { createLogger } from '@/utils/logger';

const logger = createLogger('ConfigRoutes');

export function configRoutes(configManager: ConfigManager): Router {
  const router = Router();

  /**
   * GET /api/config
   * Get current configuration
   */
  router.get('/', (req: Request, res: Response) => {
    try {
      const config = configManager.getConfig();
      
      // Remove sensitive information
      const safeConfig = {
        ...config,
        web: {
          ...config.web,
          security: {
            ...config.web.security,
            authentication: {
              ...config.web.security.authentication,
              secret: config.web.security.authentication.secret ? '[HIDDEN]' : undefined,
            },
          },
        },
      };

      res.json({
        success: true,
        data: {
          config: safeConfig,
        },
      });

    } catch (error) {
      logger.error('Failed to get configuration:', error);
      res.status(500).json({
        error: 'Failed to retrieve configuration',
        code: 'CONFIG_ERROR',
      });
    }
  });

  /**
   * PUT /api/config
   * Update configuration
   */
  router.put('/', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const updates = req.body;

      if (!updates || typeof updates !== 'object') {
        return res.status(400).json({
          error: 'Configuration updates must be an object',
          code: 'INVALID_CONFIG',
        });
      }

      // Apply updates
      const updatePromises = Object.entries(updates).map(async ([key, value]) => {
        await configManager.set(key, value);
      });

      await Promise.all(updatePromises);

      // Validate updated configuration
      const validation = await configManager.validate();
      if (!validation.isValid) {
        return res.status(400).json({
          error: 'Configuration validation failed',
          code: 'CONFIG_VALIDATION_FAILED',
          details: validation.errors,
        });
      }

      // Save configuration
      await configManager.save();

      res.json({
        success: true,
        data: {
          message: 'Configuration updated successfully',
          updates: Object.keys(updates),
        },
      });

      logger.info(`Configuration updated: ${Object.keys(updates).join(', ')}`);

    } catch (error) {
      logger.error('Failed to update configuration:', error);
      next(error);
    }
  });

  /**
   * POST /api/config/validate
   * Validate current configuration
   */
  router.post('/validate', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const validation = await configManager.validate();

      res.json({
        success: true,
        data: {
          validation,
        },
      });

    } catch (error) {
      logger.error('Failed to validate configuration:', error);
      next(error);
    }
  });

  /**
   * POST /api/config/reset
   * Reset configuration to defaults
   */
  router.post('/reset', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { scope = 'local' } = req.body;

      if (!['local', 'user', 'global', 'all'].includes(scope)) {
        return res.status(400).json({
          error: 'Invalid scope. Must be one of: local, user, global, all',
          code: 'INVALID_SCOPE',
        });
      }

      await configManager.reset(scope);

      res.json({
        success: true,
        data: {
          message: `Configuration reset to defaults (scope: ${scope})`,
          scope,
        },
      });

      logger.info(`Configuration reset: ${scope}`);

    } catch (error) {
      logger.error('Failed to reset configuration:', error);
      next(error);
    }
  });

  /**
   * GET /api/config/schema
   * Get configuration schema
   */
  router.get('/schema', (req: Request, res: Response) => {
    try {
      const schema = {
        type: 'object',
        properties: {
          output: {
            type: 'object',
            properties: {
              defaultFormat: {
                type: 'string',
                enum: ['mermaid', 'plantuml', 'ascii', 'drawio', 'lucidchart', 'json', 'yaml', 'svg', 'png', 'pdf'],
                description: 'Default output format for diagrams',
              },
              directory: {
                type: 'string',
                description: 'Default output directory for generated diagrams',
              },
              overwrite: {
                type: 'boolean',
                description: 'Whether to overwrite existing files without confirmation',
              },
            },
          },
          logging: {
            type: 'object',
            properties: {
              level: {
                type: 'string',
                enum: ['error', 'warn', 'info', 'http', 'verbose', 'debug', 'silly'],
                description: 'Logging level',
              },
              format: {
                type: 'string',
                enum: ['json', 'simple', 'combined', 'common', 'dev', 'short', 'tiny'],
                description: 'Log format',
              },
            },
          },
          nlp: {
            type: 'object',
            properties: {
              engine: {
                type: 'string',
                enum: ['natural', 'compromise', 'custom'],
                description: 'NLP engine to use',
              },
              confidence: {
                type: 'object',
                properties: {
                  minimum: {
                    type: 'number',
                    minimum: 0,
                    maximum: 1,
                    description: 'Minimum confidence threshold',
                  },
                },
              },
            },
          },
          web: {
            type: 'object',
            properties: {
              enabled: {
                type: 'boolean',
                description: 'Whether to enable the web server',
              },
              port: {
                type: 'integer',
                minimum: 1,
                maximum: 65535,
                description: 'Port for the web server',
              },
              host: {
                type: 'string',
                description: 'Host for the web server',
              },
            },
          },
          cli: {
            type: 'object',
            properties: {
              interactive: {
                type: 'boolean',
                description: 'Enable interactive mode by default',
              },
              colors: {
                type: 'boolean',
                description: 'Enable colored output',
              },
              verbose: {
                type: 'boolean',
                description: 'Enable verbose output by default',
              },
            },
          },
        },
      };

      res.json({
        success: true,
        data: {
          schema,
        },
      });

    } catch (error) {
      logger.error('Failed to get configuration schema:', error);
      res.status(500).json({
        error: 'Failed to retrieve configuration schema',
        code: 'SCHEMA_ERROR',
      });
    }
  });

  /**
   * GET /api/config/defaults
   * Get default configuration values
   */
  router.get('/defaults', (req: Request, res: Response) => {
    try {
      // Create a new config manager to get defaults
      const defaultConfigManager = new ConfigManager();
      const defaultConfig = defaultConfigManager.getConfig();

      res.json({
        success: true,
        data: {
          config: defaultConfig,
        },
      });

    } catch (error) {
      logger.error('Failed to get default configuration:', error);
      res.status(500).json({
        error: 'Failed to retrieve default configuration',
        code: 'DEFAULTS_ERROR',
      });
    }
  });

  return router;
}

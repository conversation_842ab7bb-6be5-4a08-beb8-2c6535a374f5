/**
 * Generator types for ArchitekAI
 */

import { Architecture, Component, Connection, OutputFormat } from './architecture';
import { StyleConfig } from './architecture';

export interface Generator {
  readonly format: OutputFormat;
  readonly name: string;
  readonly version: string;
  readonly description: string;
  readonly supportedFeatures: GeneratorFeature[];
  
  generate(architecture: Architecture, options?: GeneratorOptions): Promise<GeneratorResult>;
  validate(architecture: Architecture): Promise<ValidationResult>;
  getSchema(): GeneratorSchema;
}

export interface GeneratorOptions {
  style?: StyleConfig;
  output?: OutputOptions;
  features?: FeatureOptions;
  custom?: Record<string, unknown>;
}

export interface OutputOptions {
  filename?: string;
  directory?: string;
  format?: OutputFormat;
  compression?: boolean;
  minify?: boolean;
  beautify?: boolean;
}

export interface FeatureOptions {
  includeMetadata?: boolean;
  includeComments?: boolean;
  includeValidation?: boolean;
  includeTimestamp?: boolean;
  includeVersion?: boolean;
}

export interface GeneratorResult {
  content: string;
  metadata: GeneratorMetadata;
  warnings: GeneratorWarning[];
  statistics: GeneratorStatistics;
}

export interface GeneratorMetadata {
  format: OutputFormat;
  generator: string;
  version: string;
  timestamp: Date;
  architecture: {
    id: string;
    name: string;
    componentCount: number;
    connectionCount: number;
  };
  options: GeneratorOptions;
}

export interface GeneratorWarning {
  code: string;
  message: string;
  severity: 'low' | 'medium' | 'high';
  component?: string;
  suggestion?: string;
}

export interface GeneratorStatistics {
  processingTime: number;
  outputSize: number;
  compressionRatio?: number;
  complexity: ComplexityMetrics;
}

export interface ComplexityMetrics {
  cyclomatic: number;
  cognitive: number;
  structural: number;
  maintainability: number;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  suggestions: ValidationSuggestion[];
}

export interface ValidationError {
  code: string;
  message: string;
  path: string;
  severity: 'error' | 'warning';
  fix?: string;
}

export interface ValidationWarning extends ValidationError {
  impact: 'low' | 'medium' | 'high';
}

export interface ValidationSuggestion {
  type: 'optimization' | 'best-practice' | 'alternative';
  message: string;
  benefit: string;
  effort: 'low' | 'medium' | 'high';
}

export interface GeneratorSchema {
  format: OutputFormat;
  version: string;
  options: OptionSchema[];
  features: FeatureSchema[];
  examples: ExampleSchema[];
}

export interface OptionSchema {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description: string;
  required: boolean;
  default?: unknown;
  validation?: ValidationRule[];
}

export interface FeatureSchema {
  name: GeneratorFeature;
  description: string;
  supported: boolean;
  limitations?: string[];
}

export interface ExampleSchema {
  name: string;
  description: string;
  input: Partial<Architecture>;
  options?: GeneratorOptions;
  expectedOutput: string;
}

export interface ValidationRule {
  type: 'min' | 'max' | 'pattern' | 'enum' | 'custom';
  value: unknown;
  message: string;
}

export enum GeneratorFeature {
  COMPONENTS = 'components',
  CONNECTIONS = 'connections',
  STYLING = 'styling',
  METADATA = 'metadata',
  VALIDATION = 'validation',
  COMMENTS = 'comments',
  GROUPING = 'grouping',
  LAYERS = 'layers',
  ANNOTATIONS = 'annotations',
  HYPERLINKS = 'hyperlinks',
  INTERACTIVE = 'interactive',
  ANIMATION = 'animation',
  THEMES = 'themes',
  EXPORT = 'export',
  IMPORT = 'import',
}

// Specific generator interfaces

export interface MermaidGenerator extends Generator {
  generateFlowchart(architecture: Architecture, options?: MermaidOptions): Promise<GeneratorResult>;
  generateSequence(architecture: Architecture, options?: MermaidOptions): Promise<GeneratorResult>;
  generateClass(architecture: Architecture, options?: MermaidOptions): Promise<GeneratorResult>;
  generateState(architecture: Architecture, options?: MermaidOptions): Promise<GeneratorResult>;
  generateGantt(architecture: Architecture, options?: MermaidOptions): Promise<GeneratorResult>;
}

export interface MermaidOptions extends GeneratorOptions {
  diagramType?: MermaidDiagramType;
  direction?: MermaidDirection;
  theme?: MermaidTheme;
  config?: MermaidConfig;
}

export interface MermaidConfig {
  theme?: string;
  themeVariables?: Record<string, string>;
  flowchart?: FlowchartConfig;
  sequence?: SequenceConfig;
  class?: ClassConfig;
}

export interface FlowchartConfig {
  nodeSpacing?: number;
  rankSpacing?: number;
  curve?: 'basis' | 'linear' | 'cardinal';
  padding?: number;
}

export interface SequenceConfig {
  diagramMarginX?: number;
  diagramMarginY?: number;
  actorMargin?: number;
  width?: number;
  height?: number;
}

export interface ClassConfig {
  arrowMarkerAbsolute?: boolean;
  hideEmptyMembersBox?: boolean;
  dividerMargin?: number;
  padding?: number;
}

export enum MermaidDiagramType {
  FLOWCHART = 'flowchart',
  SEQUENCE = 'sequence',
  CLASS = 'class',
  STATE = 'state',
  GANTT = 'gantt',
  PIE = 'pie',
  JOURNEY = 'journey',
  GITGRAPH = 'gitgraph',
}

export enum MermaidDirection {
  TOP_DOWN = 'TD',
  TOP_BOTTOM = 'TB',
  BOTTOM_TOP = 'BT',
  RIGHT_LEFT = 'RL',
  LEFT_RIGHT = 'LR',
}

export enum MermaidTheme {
  DEFAULT = 'default',
  NEUTRAL = 'neutral',
  DARK = 'dark',
  FOREST = 'forest',
  BASE = 'base',
}

export interface PlantUMLGenerator extends Generator {
  generateComponent(architecture: Architecture, options?: PlantUMLOptions): Promise<GeneratorResult>;
  generateDeployment(architecture: Architecture, options?: PlantUMLOptions): Promise<GeneratorResult>;
  generateSequence(architecture: Architecture, options?: PlantUMLOptions): Promise<GeneratorResult>;
  generateClass(architecture: Architecture, options?: PlantUMLOptions): Promise<GeneratorResult>;
  generateActivity(architecture: Architecture, options?: PlantUMLOptions): Promise<GeneratorResult>;
}

export interface PlantUMLOptions extends GeneratorOptions {
  diagramType?: PlantUMLDiagramType;
  skinparam?: Record<string, string>;
  stereotypes?: boolean;
  notes?: boolean;
}

export enum PlantUMLDiagramType {
  COMPONENT = 'component',
  DEPLOYMENT = 'deployment',
  SEQUENCE = 'sequence',
  CLASS = 'class',
  ACTIVITY = 'activity',
  USE_CASE = 'usecase',
  STATE = 'state',
  OBJECT = 'object',
}

export interface ASCIIGenerator extends Generator {
  generateBox(architecture: Architecture, options?: ASCIIOptions): Promise<GeneratorResult>;
  generateTree(architecture: Architecture, options?: ASCIIOptions): Promise<GeneratorResult>;
  generateFlow(architecture: Architecture, options?: ASCIIOptions): Promise<GeneratorResult>;
}

export interface ASCIIOptions extends GeneratorOptions {
  style?: ASCIIStyle;
  width?: number;
  height?: number;
  padding?: number;
  borders?: boolean;
}

export enum ASCIIStyle {
  BOX = 'box',
  ROUNDED = 'rounded',
  DOUBLE = 'double',
  THICK = 'thick',
  DASHED = 'dashed',
  DOTTED = 'dotted',
}

export interface DrawIOGenerator extends Generator {
  generateXML(architecture: Architecture, options?: DrawIOOptions): Promise<GeneratorResult>;
}

export interface DrawIOOptions extends GeneratorOptions {
  compressed?: boolean;
  version?: string;
  gridSize?: number;
  guides?: boolean;
  tooltips?: boolean;
}

export interface LucidchartGenerator extends Generator {
  generateJSON(architecture: Architecture, options?: LucidchartOptions): Promise<GeneratorResult>;
}

export interface LucidchartOptions extends GeneratorOptions {
  version?: string;
  template?: string;
  collaboration?: boolean;
  permissions?: LucidchartPermissions;
}

export interface LucidchartPermissions {
  view: string[];
  edit: string[];
  comment: string[];
  share: string[];
}

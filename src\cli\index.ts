#!/usr/bin/env node

/**
 * ArchitekAI CLI Entry Point
 * 
 * Main entry point for the ArchitekAI command-line interface.
 * Provides intelligent architecture diagram generation from natural language descriptions.
 */

import { Command } from 'commander';
import chalk from 'chalk';
import figlet from 'figlet';
import { createLogger } from '@/utils/logger';
import { ConfigManager } from '@/core/config/manager';
import { generateCommand } from './commands/generate';
import { templateCommand } from './commands/template';
import { configCommand } from './commands/config';
import { batchCommand } from './commands/batch';
import { exportCommand } from './commands/export';
import { getVersion, displayBanner, handleError } from './utils';

const logger = createLogger('CLI');

async function main(): Promise<void> {
  try {
    // Initialize configuration
    const configManager = new ConfigManager();
    await configManager.initialize();

    // Create main program
    const program = new Command();

    // Program configuration
    program
      .name('architekAI')
      .description('Intelligent Architecture Diagram Generator')
      .version(getVersion(), '-v, --version', 'display version number')
      .helpOption('-h, --help', 'display help for command')
      .configureHelp({
        sortSubcommands: true,
        subcommandTerm: (cmd) => cmd.name(),
      });

    // Global options
    program
      .option('--config <path>', 'path to configuration file')
      .option('--verbose', 'enable verbose logging')
      .option('--quiet', 'suppress non-error output')
      .option('--no-colors', 'disable colored output')
      .option('--no-interactive', 'disable interactive prompts')
      .hook('preAction', async (thisCommand) => {
        const opts = thisCommand.opts();
        
        // Configure logging based on options
        if (opts.verbose) {
          logger.level = 'debug';
        } else if (opts.quiet) {
          logger.level = 'error';
        }

        // Load custom config if specified
        if (opts.config) {
          await configManager.loadConfig(opts.config);
        }

        // Configure colors
        if (!opts.colors) {
          chalk.level = 0;
        }
      });

    // Add commands
    program.addCommand(generateCommand(configManager));
    program.addCommand(templateCommand(configManager));
    program.addCommand(configCommand(configManager));
    program.addCommand(batchCommand(configManager));
    program.addCommand(exportCommand(configManager));

    // Add help examples
    program.addHelpText('after', `
Examples:
  ${chalk.cyan('architekAI generate "microservices with API gateway and user service"')}
  ${chalk.cyan('architekAI template use microservices --customize')}
  ${chalk.cyan('architekAI config set --output-format mermaid')}
  ${chalk.cyan('architekAI batch --input ./descriptions/*.txt')}
  ${chalk.cyan('architekAI export --format draw.io --output ./diagrams/')}

For more information, visit: ${chalk.blue('https://github.com/HectorTa1989/ArchitekAI')}
    `);

    // Display banner for main command without arguments
    if (process.argv.length === 2) {
      displayBanner();
      program.help();
      return;
    }

    // Parse arguments and execute
    await program.parseAsync(process.argv);

  } catch (error) {
    handleError(error);
    process.exit(1);
  }
}

// Handle uncaught exceptions and rejections
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  handleError(error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  handleError(reason);
  process.exit(1);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  logger.info('Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

// Start the application
if (require.main === module) {
  main().catch((error) => {
    handleError(error);
    process.exit(1);
  });
}

export { main };

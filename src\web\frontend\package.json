{"name": "architek-ai-frontend", "version": "1.0.0", "description": "ArchitekAI Web Frontend - React application for intelligent architecture diagram generation", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "zustand": "^4.4.7", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "react-hot-toast": "^2.4.1", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "mermaid": "^10.6.1", "monaco-editor": "^0.44.0", "@monaco-editor/react": "^4.6.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "lucide-react": "^0.294.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "c8": "^8.0.1", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}
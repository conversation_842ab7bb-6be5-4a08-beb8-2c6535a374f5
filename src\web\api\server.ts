/**
 * ArchitekAI Web API Server
 * 
 * Express.js server providing REST API endpoints for diagram generation.
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import { ConfigManager } from '@/core/config/manager';
import { NLPEngine } from '@/core/nlp/parser';
import { GeneratorFactory } from '@/core/generators/factory';
import { TemplateManager } from '@/core/templates/manager';
import { createLogger } from '@/utils/logger';
import { generateRoutes } from './routes/generate';
import { templateRoutes } from './routes/templates';
import { configRoutes } from './routes/config';
import { healthRoutes } from './routes/health';
import { errorHandler, notFoundHandler } from './middleware/error';
import { requestLogger } from './middleware/logging';
import { validateRequest } from './middleware/validation';

const logger = createLogger('WebServer');

export class ArchitekAIServer {
  private app: express.Application;
  private configManager: ConfigManager;
  private nlpEngine: NLPEngine;
  private generatorFactory: GeneratorFactory;
  private templateManager: TemplateManager;
  private server: any;

  constructor() {
    this.app = express();
    this.configManager = new ConfigManager();
    this.generatorFactory = new GeneratorFactory();
    this.templateManager = new TemplateManager({
      directory: './templates',
      autoLoad: true,
      validation: true,
      customTemplates: [],
    });
  }

  /**
   * Initialize the server
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Initializing ArchitekAI Web Server...');
      
      // Initialize configuration
      await this.configManager.initialize();
      const config = this.configManager.getConfig();
      
      // Initialize NLP engine
      this.nlpEngine = new NLPEngine(config.nlp);
      
      // Initialize template manager
      await this.templateManager.initialize();
      
      // Setup middleware
      this.setupMiddleware();
      
      // Setup routes
      this.setupRoutes();
      
      // Setup error handling
      this.setupErrorHandling();
      
      logger.info('Server initialization completed');
      
    } catch (error) {
      logger.error('Failed to initialize server:', error);
      throw new Error(`Server initialization failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Start the server
   */
  async start(): Promise<void> {
    try {
      const config = this.configManager.getConfig();
      const port = config.web.port;
      const host = config.web.host;
      
      this.server = this.app.listen(port, host, () => {
        logger.info(`ArchitekAI Web Server started on http://${host}:${port}`);
        logger.info(`Environment: ${config.environment}`);
        logger.info(`API Documentation: http://${host}:${port}/api/docs`);
      });
      
      // Handle server errors
      this.server.on('error', (error: any) => {
        if (error.code === 'EADDRINUSE') {
          logger.error(`Port ${port} is already in use`);
        } else {
          logger.error('Server error:', error);
        }
        process.exit(1);
      });
      
    } catch (error) {
      logger.error('Failed to start server:', error);
      throw new Error(`Server start failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Stop the server
   */
  async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          logger.info('Server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  /**
   * Get the Express app instance
   */
  getApp(): express.Application {
    return this.app;
  }

  /**
   * Setup middleware
   */
  private setupMiddleware(): void {
    const config = this.configManager.getConfig();
    
    // Security middleware
    if (config.web.security.helmet) {
      this.app.use(helmet({
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
          },
        },
      }));
    }
    
    // CORS middleware
    if (config.web.cors.enabled) {
      this.app.use(cors({
        origin: config.web.cors.origin,
        methods: config.web.cors.methods,
        allowedHeaders: config.web.cors.allowedHeaders,
        credentials: config.web.cors.credentials,
      }));
    }
    
    // Rate limiting
    if (config.web.rateLimit.enabled) {
      const limiter = rateLimit({
        windowMs: config.web.rateLimit.windowMs,
        max: config.web.rateLimit.max,
        message: {
          error: config.web.rateLimit.message,
          retryAfter: Math.ceil(config.web.rateLimit.windowMs / 1000),
        },
        standardHeaders: config.web.rateLimit.standardHeaders,
        legacyHeaders: config.web.rateLimit.legacyHeaders,
      });
      
      this.app.use('/api/', limiter);
    }
    
    // Request logging
    if (config.environment !== 'test') {
      this.app.use(morgan('combined', {
        stream: {
          write: (message: string) => {
            logger.info(message.trim());
          },
        },
      }));
    }
    
    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    
    // Custom middleware
    this.app.use(requestLogger);
    this.app.use(validateRequest);
    
    // Static files
    if (config.web.static.enabled) {
      this.app.use(express.static(config.web.static.directory, {
        maxAge: config.web.static.maxAge,
        etag: config.web.static.etag,
        index: config.web.static.index,
      }));
    }
  }

  /**
   * Setup routes
   */
  private setupRoutes(): void {
    // Health check routes
    this.app.use('/health', healthRoutes());
    this.app.use('/api/health', healthRoutes());
    
    // API routes
    this.app.use('/api/generate', generateRoutes(this.nlpEngine, this.generatorFactory, this.templateManager));
    this.app.use('/api/templates', templateRoutes(this.templateManager));
    this.app.use('/api/config', configRoutes(this.configManager));
    
    // API documentation
    this.app.get('/api/docs', (req, res) => {
      res.json({
        name: 'ArchitekAI API',
        version: '1.0.0',
        description: 'REST API for intelligent architecture diagram generation',
        endpoints: {
          health: {
            'GET /health': 'Health check',
            'GET /api/health': 'Detailed health status',
          },
          generation: {
            'POST /api/generate': 'Generate diagram from description',
            'POST /api/generate/batch': 'Batch generate diagrams',
            'GET /api/generate/formats': 'Get supported output formats',
          },
          templates: {
            'GET /api/templates': 'List all templates',
            'GET /api/templates/:id': 'Get specific template',
            'POST /api/templates': 'Create new template',
            'PUT /api/templates/:id': 'Update template',
            'DELETE /api/templates/:id': 'Delete template',
          },
          configuration: {
            'GET /api/config': 'Get current configuration',
            'PUT /api/config': 'Update configuration',
            'POST /api/config/validate': 'Validate configuration',
          },
        },
        examples: {
          generateDiagram: {
            url: 'POST /api/generate',
            body: {
              description: 'microservices architecture with API gateway and user service',
              format: 'mermaid',
              options: {
                interactive: false,
                validate: true,
              },
            },
          },
          listTemplates: {
            url: 'GET /api/templates',
            query: {
              pattern: 'microservices',
              complexity: 'moderate',
            },
          },
        },
      });
    });
    
    // Root endpoint
    this.app.get('/', (req, res) => {
      res.json({
        name: 'ArchitekAI',
        version: '1.0.0',
        description: 'Intelligent Architecture Diagram Generator',
        status: 'running',
        timestamp: new Date().toISOString(),
        endpoints: {
          api: '/api/docs',
          health: '/health',
          generate: '/api/generate',
          templates: '/api/templates',
        },
      });
    });
  }

  /**
   * Setup error handling
   */
  private setupErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);
    
    // Global error handler
    this.app.use(errorHandler);
  }
}

/**
 * Start the server if this file is run directly
 */
async function startServer(): Promise<void> {
  const server = new ArchitekAIServer();
  
  try {
    await server.initialize();
    await server.start();
    
    // Graceful shutdown
    process.on('SIGTERM', async () => {
      logger.info('SIGTERM received, shutting down gracefully...');
      await server.stop();
      process.exit(0);
    });
    
    process.on('SIGINT', async () => {
      logger.info('SIGINT received, shutting down gracefully...');
      await server.stop();
      process.exit(0);
    });
    
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Start server if this file is executed directly
if (require.main === module) {
  startServer().catch((error) => {
    console.error('Failed to start server:', error);
    process.exit(1);
  });
}

export { ArchitekAIServer };

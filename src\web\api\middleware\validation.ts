/**
 * Validation Middleware for ArchitekAI Web API
 * 
 * Provides request validation and sanitization.
 */

import { Request, Response, NextFunction } from 'express';
import Jo<PERSON> from 'joi';
import { createLogger } from '@/utils/logger';
import { ValidationError } from './error';

const logger = createLogger('ValidationMiddleware');

/**
 * General request validation middleware
 */
export function validateRequest(req: Request, res: Response, next: NextFunction): void {
  // Validate content type for POST/PUT requests
  if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
    const contentType = req.get('Content-Type');
    
    if (!contentType) {
      return next(new ValidationError('Content-Type header is required'));
    }
    
    if (!contentType.includes('application/json')) {
      return next(new ValidationError('Content-Type must be application/json'));
    }
  }

  // Validate request size
  const contentLength = parseInt(req.get('Content-Length') || '0');
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  if (contentLength > maxSize) {
    return next(new ValidationError('Request body too large'));
  }

  // Sanitize query parameters
  if (req.query) {
    req.query = sanitizeObject(req.query);
  }

  // Sanitize request body
  if (req.body) {
    req.body = sanitizeObject(req.body);
  }

  next();
}

/**
 * Create validation middleware for specific schema
 */
export function validateSchema(schema: Joi.ObjectSchema, target: 'body' | 'query' | 'params' = 'body') {
  return (req: Request, res: Response, next: NextFunction) => {
    const data = req[target];
    
    const { error, value } = schema.validate(data, {
      abortEarly: false,
      stripUnknown: true,
      convert: true,
    });

    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
      }));

      return next(new ValidationError('Validation failed', details));
    }

    // Replace with validated and sanitized data
    req[target] = value;
    next();
  };
}

/**
 * Validation schemas for common requests
 */
export const schemas = {
  // Generate diagram request
  generateRequest: Joi.object({
    description: Joi.string()
      .min(10)
      .max(10000)
      .required()
      .messages({
        'string.min': 'Description must be at least 10 characters long',
        'string.max': 'Description must not exceed 10,000 characters',
        'any.required': 'Description is required',
      }),
    format: Joi.string()
      .valid('mermaid', 'plantuml', 'ascii', 'drawio', 'lucidchart', 'json', 'yaml', 'svg', 'png', 'pdf')
      .default('mermaid'),
    template: Joi.string()
      .alphanum()
      .min(1)
      .max(50)
      .optional(),
    options: Joi.object({
      interactive: Joi.boolean().default(false),
      validate: Joi.boolean().default(true),
      style: Joi.object().optional(),
      features: Joi.object().optional(),
    }).default({}),
  }),

  // Batch generate request
  batchGenerateRequest: Joi.object({
    descriptions: Joi.array()
      .items(Joi.string().min(10).max(10000))
      .min(1)
      .max(50)
      .required()
      .messages({
        'array.min': 'At least one description is required',
        'array.max': 'Maximum 50 descriptions allowed per batch',
      }),
    format: Joi.string()
      .valid('mermaid', 'plantuml', 'ascii', 'drawio', 'lucidchart', 'json', 'yaml', 'svg', 'png', 'pdf')
      .default('mermaid'),
    template: Joi.string()
      .alphanum()
      .min(1)
      .max(50)
      .optional(),
    options: Joi.object({
      parallel: Joi.number().integer().min(1).max(10).default(4),
      validate: Joi.boolean().default(false),
      style: Joi.object().optional(),
    }).default({}),
  }),

  // Template creation request
  createTemplateRequest: Joi.object({
    id: Joi.string()
      .alphanum()
      .min(1)
      .max(50)
      .required()
      .messages({
        'string.alphanum': 'Template ID must contain only alphanumeric characters',
        'string.min': 'Template ID must be at least 1 character long',
        'string.max': 'Template ID must not exceed 50 characters',
      }),
    name: Joi.string()
      .min(1)
      .max(100)
      .required(),
    description: Joi.string()
      .min(10)
      .max(1000)
      .required(),
    pattern: Joi.string()
      .valid(
        'microservices', 'monolithic', 'serverless', 'event_driven', 'layered',
        'hexagonal', 'clean_architecture', 'mvc', 'mvp', 'mvvm', 'pipe_and_filter',
        'blackboard', 'broker', 'peer_to_peer', 'service_oriented', 'space_based',
        'master_slave', 'custom'
      )
      .required(),
    components: Joi.array()
      .items(Joi.object({
        id: Joi.string().required(),
        name: Joi.string().required(),
        type: Joi.string().required(),
        description: Joi.string().optional(),
        properties: Joi.object().optional(),
        position: Joi.object({
          x: Joi.number().required(),
          y: Joi.number().required(),
        }).optional(),
      }))
      .min(1)
      .required(),
    connections: Joi.array()
      .items(Joi.object({
        id: Joi.string().optional(),
        source: Joi.string().required(),
        target: Joi.string().required(),
        type: Joi.string().required(),
        label: Joi.string().optional(),
        properties: Joi.object().optional(),
        bidirectional: Joi.boolean().optional(),
      }))
      .default([]),
    metadata: Joi.object({
      version: Joi.string().default('1.0.0'),
      author: Joi.string().optional(),
      tags: Joi.array().items(Joi.string()).default([]),
      complexity: Joi.string().valid('simple', 'moderate', 'complex', 'expert').default('moderate'),
      useCases: Joi.array().items(Joi.string()).default([]),
      prerequisites: Joi.array().items(Joi.string()).default([]),
      documentation: Joi.string().default(''),
    }).default({}),
    customization: Joi.object({
      allowComponentAddition: Joi.boolean().default(true),
      allowComponentRemoval: Joi.boolean().default(true),
      allowConnectionModification: Joi.boolean().default(true),
      requiredComponents: Joi.array().items(Joi.string()).default([]),
      optionalComponents: Joi.array().items(Joi.string()).default([]),
      configurationSchema: Joi.object().default({}),
    }).default({}),
  }),

  // Configuration update request
  configUpdateRequest: Joi.object({
    'output.defaultFormat': Joi.string()
      .valid('mermaid', 'plantuml', 'ascii', 'drawio', 'lucidchart', 'json', 'yaml', 'svg', 'png', 'pdf')
      .optional(),
    'output.directory': Joi.string().min(1).max(500).optional(),
    'output.overwrite': Joi.boolean().optional(),
    'logging.level': Joi.string()
      .valid('error', 'warn', 'info', 'http', 'verbose', 'debug', 'silly')
      .optional(),
    'logging.format': Joi.string()
      .valid('json', 'simple', 'combined', 'common', 'dev', 'short', 'tiny')
      .optional(),
    'nlp.engine': Joi.string()
      .valid('natural', 'compromise', 'custom')
      .optional(),
    'nlp.confidence.minimum': Joi.number().min(0).max(1).optional(),
    'web.enabled': Joi.boolean().optional(),
    'web.port': Joi.number().integer().min(1).max(65535).optional(),
    'web.host': Joi.string().hostname().optional(),
    'cli.interactive': Joi.boolean().optional(),
    'cli.colors': Joi.boolean().optional(),
    'cli.verbose': Joi.boolean().optional(),
  }).min(1),

  // Query parameters for template listing
  templateListQuery: Joi.object({
    pattern: Joi.string()
      .valid(
        'microservices', 'monolithic', 'serverless', 'event_driven', 'layered',
        'hexagonal', 'clean_architecture', 'mvc', 'mvp', 'mvvm', 'pipe_and_filter',
        'blackboard', 'broker', 'peer_to_peer', 'service_oriented', 'space_based',
        'master_slave', 'custom'
      )
      .optional(),
    complexity: Joi.string()
      .valid('simple', 'moderate', 'complex', 'expert')
      .optional(),
    tags: Joi.string()
      .pattern(/^[a-zA-Z0-9,\s-]+$/)
      .optional(),
    search: Joi.string()
      .min(1)
      .max(100)
      .optional(),
  }),

  // Validation request
  validateDescriptionRequest: Joi.object({
    description: Joi.string()
      .min(1)
      .max(10000)
      .required(),
  }),
};

/**
 * Sanitize object by removing potentially dangerous content
 */
function sanitizeObject(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'string') {
    return sanitizeString(obj);
  }

  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }

  if (typeof obj === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const sanitizedKey = sanitizeString(key);
      sanitized[sanitizedKey] = sanitizeObject(value);
    }
    return sanitized;
  }

  return obj;
}

/**
 * Sanitize string by removing potentially dangerous content
 */
function sanitizeString(str: string): string {
  if (typeof str !== 'string') {
    return str;
  }

  // Remove null bytes
  str = str.replace(/\0/g, '');

  // Remove control characters except newlines and tabs
  str = str.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

  // Limit length
  if (str.length > 10000) {
    str = str.substring(0, 10000);
  }

  return str;
}

/**
 * Rate limiting validation
 */
export function validateRateLimit(req: Request, res: Response, next: NextFunction): void {
  // Check for rate limit headers
  const remaining = parseInt(res.get('X-RateLimit-Remaining') || '0');
  
  if (remaining <= 0) {
    const resetTime = parseInt(res.get('X-RateLimit-Reset') || '0');
    const retryAfter = Math.max(0, resetTime - Math.floor(Date.now() / 1000));
    
    return next(new ValidationError('Rate limit exceeded', { retryAfter }));
  }

  next();
}

/**
 * File upload validation
 */
export function validateFileUpload(req: Request, res: Response, next: NextFunction): void {
  if (!req.file && !req.files) {
    return next();
  }

  const allowedMimeTypes = [
    'text/plain',
    'application/json',
    'application/yaml',
    'text/yaml',
    'application/x-yaml',
  ];

  const maxFileSize = 5 * 1024 * 1024; // 5MB

  const files = req.files ? (Array.isArray(req.files) ? req.files : [req.file]) : [req.file];

  for (const file of files) {
    if (!file) continue;

    if (!allowedMimeTypes.includes(file.mimetype)) {
      return next(new ValidationError(`Invalid file type: ${file.mimetype}. Allowed types: ${allowedMimeTypes.join(', ')}`));
    }

    if (file.size > maxFileSize) {
      return next(new ValidationError(`File too large: ${file.size} bytes. Maximum allowed: ${maxFileSize} bytes`));
    }
  }

  next();
}

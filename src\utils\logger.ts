/**
 * Logger utility for ArchitekAI
 * 
 * Provides structured logging with multiple transports and formatting options.
 */

import winston from 'winston';
import path from 'path';
import fs from 'fs-extra';
import { LogLevel, LogFormat, LogOutput } from '@/types/config';

// Default log directory
const LOG_DIR = path.join(process.cwd(), 'logs');

// Ensure log directory exists
fs.ensureDirSync(LOG_DIR);

// Custom log levels
const customLevels = {
  levels: {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    verbose: 4,
    debug: 5,
    silly: 6,
  },
  colors: {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    http: 'magenta',
    verbose: 'cyan',
    debug: 'blue',
    silly: 'grey',
  },
};

// Add colors to winston
winston.addColors(customLevels.colors);

// Custom format for console output
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'HH:mm:ss' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
    const serviceTag = service ? `[${service}]` : '';
    const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
    return `${timestamp} ${level} ${serviceTag} ${message}${metaStr}`;
  })
);

// Custom format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create base logger configuration
const createLoggerConfig = (service?: string): winston.LoggerOptions => ({
  level: process.env.LOG_LEVEL || 'info',
  levels: customLevels.levels,
  defaultMeta: { service },
  transports: [
    // Console transport
    new winston.transports.Console({
      format: consoleFormat,
      silent: process.env.NODE_ENV === 'test',
    }),
    
    // File transport for errors
    new winston.transports.File({
      filename: path.join(LOG_DIR, 'error.log'),
      level: 'error',
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    // File transport for all logs
    new winston.transports.File({
      filename: path.join(LOG_DIR, 'combined.log'),
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
  
  // Handle exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(LOG_DIR, 'exceptions.log'),
      format: fileFormat,
    }),
  ],
  
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(LOG_DIR, 'rejections.log'),
      format: fileFormat,
    }),
  ],
});

// Logger cache to avoid creating multiple loggers for the same service
const loggerCache = new Map<string, winston.Logger>();

/**
 * Create or get a logger instance for a specific service
 */
export function createLogger(service?: string): winston.Logger {
  const key = service || 'default';
  
  if (loggerCache.has(key)) {
    return loggerCache.get(key)!;
  }
  
  const logger = winston.createLogger(createLoggerConfig(service));
  loggerCache.set(key, logger);
  
  return logger;
}

/**
 * Get the default logger instance
 */
export const logger = createLogger();

/**
 * Configure logger with custom settings
 */
export function configureLogger(config: LoggerConfig): void {
  // Clear existing loggers
  loggerCache.clear();
  
  // Update default logger configuration
  const newConfig = createLoggerConfig();
  
  // Apply custom configuration
  if (config.level) {
    newConfig.level = config.level;
  }
  
  if (config.outputs) {
    newConfig.transports = [];
    
    config.outputs.forEach(output => {
      switch (output) {
        case LogOutput.CONSOLE:
          newConfig.transports!.push(
            new winston.transports.Console({
              format: config.format === LogFormat.JSON ? fileFormat : consoleFormat,
              silent: process.env.NODE_ENV === 'test' && !config.enableInTests,
            })
          );
          break;
          
        case LogOutput.FILE:
          if (config.file) {
            newConfig.transports!.push(
              new winston.transports.File({
                filename: config.file.path,
                format: fileFormat,
                maxsize: parseSize(config.file.maxSize),
                maxFiles: config.file.maxFiles,
                compress: config.file.compress,
              })
            );
          }
          break;
          
        case LogOutput.HTTP:
          if (config.http) {
            newConfig.transports!.push(
              new winston.transports.Http({
                host: config.http.host,
                port: config.http.port,
                path: config.http.path,
              })
            );
          }
          break;
      }
    });
  }
  
  // Create new default logger
  const newLogger = winston.createLogger(newConfig);
  loggerCache.set('default', newLogger);
}

/**
 * Set log level for all loggers
 */
export function setLogLevel(level: LogLevel): void {
  loggerCache.forEach(logger => {
    logger.level = level;
  });
}

/**
 * Enable or disable console logging
 */
export function setConsoleLogging(enabled: boolean): void {
  loggerCache.forEach(logger => {
    const consoleTransport = logger.transports.find(
      transport => transport instanceof winston.transports.Console
    );
    
    if (consoleTransport) {
      (consoleTransport as winston.transports.Console).silent = !enabled;
    }
  });
}

/**
 * Create a child logger with additional metadata
 */
export function createChildLogger(parent: winston.Logger, meta: Record<string, unknown>): winston.Logger {
  return parent.child(meta);
}

/**
 * Log performance metrics
 */
export function logPerformance(
  logger: winston.Logger,
  operation: string,
  startTime: number,
  metadata?: Record<string, unknown>
): void {
  const duration = Date.now() - startTime;
  logger.info(`Performance: ${operation}`, {
    duration,
    operation,
    ...metadata,
  });
}

/**
 * Create a timer function for performance logging
 */
export function createTimer(logger: winston.Logger, operation: string): () => void {
  const startTime = Date.now();
  
  return (metadata?: Record<string, unknown>) => {
    logPerformance(logger, operation, startTime, metadata);
  };
}

/**
 * Log error with stack trace
 */
export function logError(logger: winston.Logger, error: Error, context?: string): void {
  logger.error(`${context ? `${context}: ` : ''}${error.message}`, {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    context,
  });
}

/**
 * Parse size string to bytes
 */
function parseSize(size: string): number {
  const units: Record<string, number> = {
    B: 1,
    KB: 1024,
    MB: 1024 * 1024,
    GB: 1024 * 1024 * 1024,
  };
  
  const match = size.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)$/i);
  if (!match) {
    throw new Error(`Invalid size format: ${size}`);
  }
  
  const value = parseFloat(match[1]!);
  const unit = match[2]!.toUpperCase();
  
  return Math.floor(value * units[unit]!);
}

// Type definitions
export interface LoggerConfig {
  level?: LogLevel;
  format?: LogFormat;
  outputs?: LogOutput[];
  enableInTests?: boolean;
  file?: {
    path: string;
    maxSize: string;
    maxFiles: number;
    compress: boolean;
  };
  http?: {
    host: string;
    port: number;
    path: string;
  };
}

// Export types for external use
export { LogLevel, LogFormat, LogOutput } from '@/types/config';

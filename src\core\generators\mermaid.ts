/**
 * Mermaid Diagram Generator for ArchitekAI
 * 
 * Generates Mermaid diagrams from architecture descriptions.
 */

import { 
  Generator, 
  GeneratorOptions, 
  GeneratorResult, 
  ValidationResult, 
  GeneratorSchema,
  MermaidGenerator as IMermaidGenerator,
  MermaidOptions,
  MermaidDiagramType,
  MermaidDirection,
  MermaidTheme,
  GeneratorFeature
} from '@/types/generator';
import { Architecture, Component, Connection, ComponentType, ConnectionType, OutputFormat } from '@/types/architecture';
import { createLogger } from '@/utils/logger';

const logger = createLogger('MermaidGenerator');

export class MermaidGenerator implements IMermaidGenerator {
  readonly format = OutputFormat.MERMAID;
  readonly name = 'Mermaid Generator';
  readonly version = '1.0.0';
  readonly description = 'Generates Mermaid diagrams from architecture descriptions';
  readonly supportedFeatures = [
    GeneratorFeature.COMPONENTS,
    GeneratorFeature.CONNECTIONS,
    GeneratorFeature.STYLING,
    GeneratorFeature.METADATA,
    GeneratorFeature.COMMENTS,
    GeneratorFeature.GROUPING,
    GeneratorFeature.THEMES,
  ];

  async generate(architecture: Architecture, options?: MermaidOptions): Promise<GeneratorResult> {
    const startTime = Date.now();
    
    try {
      logger.debug(`Generating Mermaid diagram for architecture: ${architecture.name}`);
      
      const mermaidOptions = options as MermaidOptions;
      const diagramType = mermaidOptions?.diagramType || MermaidDiagramType.FLOWCHART;
      
      let content: string;
      
      switch (diagramType) {
        case MermaidDiagramType.FLOWCHART:
          content = this.generateFlowchartContent(architecture, mermaidOptions);
          break;
        case MermaidDiagramType.SEQUENCE:
          content = this.generateSequenceContent(architecture, mermaidOptions);
          break;
        case MermaidDiagramType.CLASS:
          content = this.generateClassContent(architecture, mermaidOptions);
          break;
        default:
          content = this.generateFlowchartContent(architecture, mermaidOptions);
      }
      
      const processingTime = Date.now() - startTime;
      
      const result: GeneratorResult = {
        content,
        metadata: {
          format: this.format,
          generator: this.name,
          version: this.version,
          timestamp: new Date(),
          architecture: {
            id: architecture.id,
            name: architecture.name,
            componentCount: architecture.components.length,
            connectionCount: architecture.connections.length,
          },
          options: options || {},
        },
        warnings: [],
        statistics: {
          processingTime,
          outputSize: Buffer.byteLength(content, 'utf8'),
          complexity: this.calculateComplexity(architecture),
        },
      };
      
      logger.debug(`Mermaid generation completed in ${processingTime}ms`);
      
      return result;
      
    } catch (error) {
      logger.error('Failed to generate Mermaid diagram:', error);
      throw new Error(`Mermaid generation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async generateFlowchart(architecture: Architecture, options?: MermaidOptions): Promise<GeneratorResult> {
    const mermaidOptions: MermaidOptions = { ...options, diagramType: MermaidDiagramType.FLOWCHART };
    return this.generate(architecture, mermaidOptions);
  }

  async generateSequence(architecture: Architecture, options?: MermaidOptions): Promise<GeneratorResult> {
    const mermaidOptions: MermaidOptions = { ...options, diagramType: MermaidDiagramType.SEQUENCE };
    return this.generate(architecture, mermaidOptions);
  }

  async generateClass(architecture: Architecture, options?: MermaidOptions): Promise<GeneratorResult> {
    const mermaidOptions: MermaidOptions = { ...options, diagramType: MermaidDiagramType.CLASS };
    return this.generate(architecture, mermaidOptions);
  }

  async generateState(architecture: Architecture, options?: MermaidOptions): Promise<GeneratorResult> {
    const mermaidOptions: MermaidOptions = { ...options, diagramType: MermaidDiagramType.STATE };
    return this.generate(architecture, mermaidOptions);
  }

  async generateGantt(architecture: Architecture, options?: MermaidOptions): Promise<GeneratorResult> {
    const mermaidOptions: MermaidOptions = { ...options, diagramType: MermaidDiagramType.GANTT };
    return this.generate(architecture, mermaidOptions);
  }

  async validate(architecture: Architecture): Promise<ValidationResult> {
    const errors: any[] = [];
    const warnings: any[] = [];
    const suggestions: any[] = [];
    
    // Validate components
    if (architecture.components.length === 0) {
      errors.push({
        code: 'NO_COMPONENTS',
        message: 'Architecture must have at least one component',
        path: 'components',
        severity: 'error' as const,
      });
    }
    
    // Check for isolated components
    const connectedComponents = new Set<string>();
    architecture.connections.forEach(conn => {
      connectedComponents.add(conn.source);
      connectedComponents.add(conn.target);
    });
    
    architecture.components.forEach(comp => {
      if (!connectedComponents.has(comp.id) && architecture.components.length > 1) {
        warnings.push({
          code: 'ISOLATED_COMPONENT',
          message: `Component '${comp.name}' is not connected to any other component`,
          path: `components.${comp.id}`,
          severity: 'warning' as const,
          impact: 'medium' as const,
        });
      }
    });
    
    // Check for very long component names
    architecture.components.forEach(comp => {
      if (comp.name.length > 30) {
        suggestions.push({
          type: 'optimization' as const,
          message: `Consider shortening component name: '${comp.name}'`,
          benefit: 'Improved diagram readability',
          effort: 'low' as const,
        });
      }
    });
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
    };
  }

  getSchema(): GeneratorSchema {
    return {
      format: this.format,
      version: this.version,
      options: [
        {
          name: 'diagramType',
          type: 'string',
          description: 'Type of Mermaid diagram to generate',
          required: false,
          default: 'flowchart',
          validation: [
            {
              type: 'enum',
              value: Object.values(MermaidDiagramType),
              message: 'Must be a valid Mermaid diagram type',
            },
          ],
        },
        {
          name: 'direction',
          type: 'string',
          description: 'Direction of the diagram flow',
          required: false,
          default: 'TD',
          validation: [
            {
              type: 'enum',
              value: Object.values(MermaidDirection),
              message: 'Must be a valid direction (TD, TB, BT, RL, LR)',
            },
          ],
        },
        {
          name: 'theme',
          type: 'string',
          description: 'Mermaid theme to apply',
          required: false,
          default: 'default',
          validation: [
            {
              type: 'enum',
              value: Object.values(MermaidTheme),
              message: 'Must be a valid Mermaid theme',
            },
          ],
        },
      ],
      features: [
        {
          name: GeneratorFeature.COMPONENTS,
          description: 'Support for architecture components',
          supported: true,
        },
        {
          name: GeneratorFeature.CONNECTIONS,
          description: 'Support for component connections',
          supported: true,
        },
        {
          name: GeneratorFeature.STYLING,
          description: 'Support for custom styling',
          supported: true,
        },
        {
          name: GeneratorFeature.THEMES,
          description: 'Support for predefined themes',
          supported: true,
        },
        {
          name: GeneratorFeature.COMMENTS,
          description: 'Support for comments in output',
          supported: true,
        },
        {
          name: GeneratorFeature.GROUPING,
          description: 'Support for component grouping',
          supported: true,
        },
      ],
      examples: [
        {
          name: 'Simple Microservices',
          description: 'Basic microservices architecture with API gateway',
          input: {
            name: 'Simple Microservices',
            description: 'API Gateway with User and Order services',
            components: [
              { id: 'gw', name: 'API Gateway', type: ComponentType.API_GATEWAY },
              { id: 'user', name: 'User Service', type: ComponentType.SERVICE },
              { id: 'order', name: 'Order Service', type: ComponentType.SERVICE },
            ],
            connections: [
              { id: 'gw-user', source: 'gw', target: 'user', type: ConnectionType.HTTP },
              { id: 'gw-order', source: 'gw', target: 'order', type: ConnectionType.HTTP },
            ],
          },
          expectedOutput: `graph TD
    gw[API Gateway] --> user[User Service]
    gw --> order[Order Service]`,
        },
      ],
    };
  }

  private generateFlowchartContent(architecture: Architecture, options?: MermaidOptions): string {
    const direction = options?.direction || MermaidDirection.TOP_DOWN;
    const theme = options?.theme || MermaidTheme.DEFAULT;
    
    let content = `graph ${direction}\n`;
    
    // Add theme configuration if specified
    if (theme !== MermaidTheme.DEFAULT) {
      content += `%%{init: {'theme':'${theme}'}}%%\n`;
    }
    
    // Add comments if enabled
    if (options?.features?.includeComments) {
      content += `%% Generated by ArchitekAI Mermaid Generator\n`;
      content += `%% Architecture: ${architecture.name}\n`;
      content += `%% Components: ${architecture.components.length}\n`;
      content += `%% Connections: ${architecture.connections.length}\n\n`;
    }
    
    // Generate component definitions
    const componentMap = new Map<string, Component>();
    architecture.components.forEach(component => {
      componentMap.set(component.id, component);
      const nodeId = this.sanitizeId(component.id);
      const nodeLabel = this.escapeLabel(component.name);
      const nodeShape = this.getComponentShape(component.type);
      
      content += `    ${nodeId}${nodeShape[0]}${nodeLabel}${nodeShape[1]}\n`;
    });
    
    content += '\n';
    
    // Generate connections
    architecture.connections.forEach(connection => {
      const sourceId = this.sanitizeId(connection.source);
      const targetId = this.sanitizeId(connection.target);
      const arrow = this.getConnectionArrow(connection.type);
      const label = connection.label ? `|${this.escapeLabel(connection.label)}|` : '';
      
      content += `    ${sourceId} ${arrow}${label} ${targetId}\n`;
    });
    
    // Add styling if specified
    if (options?.style) {
      content += '\n';
      content += this.generateStyling(architecture, options);
    }
    
    return content;
  }

  private generateSequenceContent(architecture: Architecture, options?: MermaidOptions): string {
    let content = 'sequenceDiagram\n';
    
    if (options?.features?.includeComments) {
      content += `    %% Generated by ArchitekAI\n`;
      content += `    %% Architecture: ${architecture.name}\n\n`;
    }
    
    // Define participants
    architecture.components.forEach(component => {
      const participantId = this.sanitizeId(component.id);
      const participantName = this.escapeLabel(component.name);
      content += `    participant ${participantId} as ${participantName}\n`;
    });
    
    content += '\n';
    
    // Generate sequence interactions
    architecture.connections.forEach((connection, index) => {
      const sourceId = this.sanitizeId(connection.source);
      const targetId = this.sanitizeId(connection.target);
      const message = connection.label || `Message ${index + 1}`;
      
      content += `    ${sourceId}->>+${targetId}: ${this.escapeLabel(message)}\n`;
      content += `    ${targetId}-->>-${sourceId}: Response\n`;
    });
    
    return content;
  }

  private generateClassContent(architecture: Architecture, options?: MermaidOptions): string {
    let content = 'classDiagram\n';
    
    if (options?.features?.includeComments) {
      content += `    %% Generated by ArchitekAI\n`;
      content += `    %% Architecture: ${architecture.name}\n\n`;
    }
    
    // Generate class definitions
    architecture.components.forEach(component => {
      const className = this.sanitizeId(component.name);
      content += `    class ${className} {\n`;
      content += `        +String id\n`;
      content += `        +String name\n`;
      content += `        +ComponentType type\n`;
      
      // Add component-specific methods based on type
      const methods = this.getComponentMethods(component.type);
      methods.forEach(method => {
        content += `        +${method}\n`;
      });
      
      content += `    }\n\n`;
    });
    
    // Generate relationships
    architecture.connections.forEach(connection => {
      const sourceClass = this.sanitizeId(this.getComponentName(connection.source, architecture));
      const targetClass = this.sanitizeId(this.getComponentName(connection.target, architecture));
      const relationship = this.getClassRelationship(connection.type);
      
      content += `    ${sourceClass} ${relationship} ${targetClass}\n`;
    });
    
    return content;
  }

  private sanitizeId(id: string): string {
    return id.replace(/[^a-zA-Z0-9_]/g, '_');
  }

  private escapeLabel(label: string): string {
    return label.replace(/"/g, '\\"');
  }

  private getComponentShape(type: ComponentType): [string, string] {
    const shapes: Record<ComponentType, [string, string]> = {
      [ComponentType.SERVICE]: ['[', ']'],
      [ComponentType.DATABASE]: ['[(', ')]'],
      [ComponentType.API_GATEWAY]: ['{{', '}}'],
      [ComponentType.LOAD_BALANCER]: ['[/', '\\]'],
      [ComponentType.CACHE]: ['[[', ']]'],
      [ComponentType.QUEUE]: ['>', ']'],
      [ComponentType.EVENT_STREAM]: ['>', ']'],
      [ComponentType.FUNCTION]: ['(', ')'],
      [ComponentType.CDN]: ['{', '}'],
      [ComponentType.WEB_APP]: ['[', ']'],
      [ComponentType.MOBILE_APP]: ['[', ']'],
      [ComponentType.STORAGE]: ['[(', ')]'],
      [ComponentType.CONTAINER]: ['[', ']'],
      [ComponentType.EXTERNAL_SERVICE]: ['[[', ']]'],
      [ComponentType.USER_INTERFACE]: ['[', ']'],
      [ComponentType.DESKTOP_APP]: ['[', ']'],
      [ComponentType.IOT_DEVICE]: ['>', ']'],
      [ComponentType.NETWORK]: ['{', '}'],
      [ComponentType.SECURITY]: ['{{', '}}'],
      [ComponentType.MONITORING]: ['[', ']'],
      [ComponentType.LOGGING]: ['[', ']'],
      [ComponentType.ANALYTICS]: ['[', ']'],
      [ComponentType.NOTIFICATION]: ['>', ']'],
      [ComponentType.WORKFLOW]: ['[', ']'],
      [ComponentType.DATA_PIPELINE]: ['>', ']'],
      [ComponentType.ML_MODEL]: ['(', ')'],
      [ComponentType.CUSTOM]: ['[', ']'],
    };
    
    return shapes[type] || ['[', ']'];
  }

  private getConnectionArrow(type: ConnectionType): string {
    const arrows: Record<ConnectionType, string> = {
      [ConnectionType.HTTP]: '-->',
      [ConnectionType.HTTPS]: '==>',
      [ConnectionType.DATABASE_CONNECTION]: '-.->',
      [ConnectionType.MESSAGE_QUEUE]: '-.->',
      [ConnectionType.API_CALL]: '-->',
      [ConnectionType.TCP]: '-->',
      [ConnectionType.UDP]: '-.->',
      [ConnectionType.WEBSOCKET]: '<-->',
      [ConnectionType.GRPC]: '==>',
      [ConnectionType.GRAPHQL]: '-->',
      [ConnectionType.REST]: '-->',
      [ConnectionType.SOAP]: '-->',
      [ConnectionType.EVENT_STREAM]: '-.->',
      [ConnectionType.FILE_SYSTEM]: '-.->',
      [ConnectionType.NETWORK]: '---',
      [ConnectionType.VPN]: '==>',
      [ConnectionType.DEPENDENCY]: '-.->',
      [ConnectionType.DATA_FLOW]: '-->',
      [ConnectionType.CONTROL_FLOW]: '-->',
      [ConnectionType.INHERITANCE]: '--|>',
      [ConnectionType.COMPOSITION]: '--*',
      [ConnectionType.AGGREGATION]: '--o',
      [ConnectionType.ASSOCIATION]: '-->',
      [ConnectionType.CUSTOM]: '-->',
    };
    
    return arrows[type] || '-->';
  }

  private getComponentMethods(type: ComponentType): string[] {
    const methods: Record<ComponentType, string[]> = {
      [ComponentType.SERVICE]: ['start()', 'stop()', 'processRequest()'],
      [ComponentType.DATABASE]: ['connect()', 'query()', 'disconnect()'],
      [ComponentType.API_GATEWAY]: ['route()', 'authenticate()', 'rateLimit()'],
      [ComponentType.LOAD_BALANCER]: ['distribute()', 'healthCheck()'],
      [ComponentType.CACHE]: ['get()', 'set()', 'invalidate()'],
      [ComponentType.QUEUE]: ['enqueue()', 'dequeue()', 'peek()'],
      [ComponentType.EVENT_STREAM]: ['publish()', 'subscribe()', 'filter()'],
      [ComponentType.FUNCTION]: ['invoke()', 'configure()'],
      [ComponentType.CDN]: ['cache()', 'serve()', 'purge()'],
      [ComponentType.WEB_APP]: ['render()', 'handleEvent()'],
      [ComponentType.MOBILE_APP]: ['launch()', 'navigate()'],
      [ComponentType.STORAGE]: ['store()', 'retrieve()', 'delete()'],
      [ComponentType.CONTAINER]: ['start()', 'stop()', 'scale()'],
      [ComponentType.EXTERNAL_SERVICE]: ['call()', 'authenticate()'],
      [ComponentType.USER_INTERFACE]: ['display()', 'interact()'],
      [ComponentType.DESKTOP_APP]: ['launch()', 'minimize()'],
      [ComponentType.IOT_DEVICE]: ['sense()', 'transmit()'],
      [ComponentType.NETWORK]: ['route()', 'forward()'],
      [ComponentType.SECURITY]: ['authenticate()', 'authorize()'],
      [ComponentType.MONITORING]: ['collect()', 'alert()'],
      [ComponentType.LOGGING]: ['log()', 'rotate()'],
      [ComponentType.ANALYTICS]: ['analyze()', 'report()'],
      [ComponentType.NOTIFICATION]: ['send()', 'schedule()'],
      [ComponentType.WORKFLOW]: ['execute()', 'orchestrate()'],
      [ComponentType.DATA_PIPELINE]: ['extract()', 'transform()', 'load()'],
      [ComponentType.ML_MODEL]: ['train()', 'predict()'],
      [ComponentType.CUSTOM]: ['execute()'],
    };
    
    return methods[type] || ['execute()'];
  }

  private getClassRelationship(type: ConnectionType): string {
    const relationships: Record<ConnectionType, string> = {
      [ConnectionType.HTTP]: '-->',
      [ConnectionType.HTTPS]: '==>',
      [ConnectionType.TCP]: '-->',
      [ConnectionType.UDP]: '-->',
      [ConnectionType.WEBSOCKET]: '==>',
      [ConnectionType.GRPC]: '==>',
      [ConnectionType.GRAPHQL]: '-->',
      [ConnectionType.REST]: '-->',
      [ConnectionType.SOAP]: '-->',
      [ConnectionType.MESSAGE_QUEUE]: '..>',
      [ConnectionType.EVENT_STREAM]: '..>',
      [ConnectionType.DATABASE_CONNECTION]: '..>',
      [ConnectionType.FILE_SYSTEM]: '..>',
      [ConnectionType.NETWORK]: '-->',
      [ConnectionType.VPN]: '==>',
      [ConnectionType.API_CALL]: '-->',
      [ConnectionType.DEPENDENCY]: '..>',
      [ConnectionType.DATA_FLOW]: '-->',
      [ConnectionType.CONTROL_FLOW]: '-->',
      [ConnectionType.INHERITANCE]: '--|>',
      [ConnectionType.COMPOSITION]: '--*',
      [ConnectionType.AGGREGATION]: '--o',
      [ConnectionType.ASSOCIATION]: '-->',
      [ConnectionType.CUSTOM]: '-->',
    };
    
    return relationships[type] || '-->';
  }

  private getComponentName(componentId: string, architecture: Architecture): string {
    const component = architecture.components.find(c => c.id === componentId);
    return component?.name || componentId;
  }

  private generateStyling(architecture: Architecture, options: MermaidOptions): string {
    let styling = '';
    
    // Add component styling based on type
    architecture.components.forEach((component, index) => {
      const nodeId = this.sanitizeId(component.id);
      const styleClass = this.getComponentStyleClass(component.type);
      styling += `    classDef ${styleClass} fill:#${this.getComponentColor(component.type)}\n`;
      styling += `    class ${nodeId} ${styleClass}\n`;
    });
    
    return styling;
  }

  private getComponentStyleClass(type: ComponentType): string {
    return `style_${type.toLowerCase()}`;
  }

  private getComponentColor(type: ComponentType): string {
    const colors: Record<ComponentType, string> = {
      [ComponentType.SERVICE]: 'e1f5fe',
      [ComponentType.DATABASE]: 'f3e5f5',
      [ComponentType.API_GATEWAY]: 'fff3e0',
      [ComponentType.LOAD_BALANCER]: 'e8f5e8',
      [ComponentType.CACHE]: 'fce4ec',
      [ComponentType.QUEUE]: 'f1f8e9',
      [ComponentType.FUNCTION]: 'e0f2f1',
      [ComponentType.CDN]: 'fff8e1',
      [ComponentType.WEB_APP]: 'e3f2fd',
      [ComponentType.MOBILE_APP]: 'e8eaf6',
      [ComponentType.STORAGE]: 'f3e5f5',
      [ComponentType.CONTAINER]: 'e1f5fe',
      [ComponentType.EXTERNAL_SERVICE]: 'fafafa',
      [ComponentType.USER_INTERFACE]: 'e3f2fd',
      [ComponentType.DESKTOP_APP]: 'e8eaf6',
      [ComponentType.IOT_DEVICE]: 'f1f8e9',
      [ComponentType.NETWORK]: 'fff8e1',
      [ComponentType.SECURITY]: 'ffebee',
      [ComponentType.MONITORING]: 'e0f2f1',
      [ComponentType.LOGGING]: 'f1f8e9',
      [ComponentType.ANALYTICS]: 'e8f5e8',
      [ComponentType.NOTIFICATION]: 'fff3e0',
      [ComponentType.WORKFLOW]: 'f3e5f5',
      [ComponentType.DATA_PIPELINE]: 'e1f5fe',
      [ComponentType.ML_MODEL]: 'fce4ec',
      [ComponentType.CUSTOM]: 'f5f5f5',
    };
    
    return colors[type] || 'f5f5f5';
  }

  private calculateComplexity(architecture: Architecture): any {
    const componentCount = architecture.components.length;
    const connectionCount = architecture.connections.length;
    
    // Simple complexity metrics
    const cyclomatic = connectionCount - componentCount + 2;
    const cognitive = Math.floor(componentCount / 3) + Math.floor(connectionCount / 2);
    const structural = componentCount + connectionCount;
    const maintainability = Math.max(0, 100 - (structural * 2));
    
    return {
      cyclomatic: Math.max(1, cyclomatic),
      cognitive: Math.max(1, cognitive),
      structural,
      maintainability,
    };
  }
}

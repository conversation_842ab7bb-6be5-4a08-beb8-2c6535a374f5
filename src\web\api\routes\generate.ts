/**
 * Generation API Routes for ArchitekAI
 * 
 * Handles diagram generation requests via REST API.
 */

import { Router, Request, Response, NextFunction } from 'express';
import { NLPEngine } from '@/core/nlp/parser';
import { GeneratorFactory } from '@/core/generators/factory';
import { TemplateManager } from '@/core/templates/manager';
import { OutputFormat } from '@/types/architecture';
import { createLogger } from '@/utils/logger';

const logger = createLogger('GenerateRoutes');

interface GenerateRequest {
  description: string;
  format?: OutputFormat;
  template?: string;
  options?: {
    interactive?: boolean;
    validate?: boolean;
    style?: any;
    features?: any;
  };
}

interface BatchGenerateRequest {
  descriptions: string[];
  format?: OutputFormat;
  template?: string;
  options?: {
    parallel?: number;
    validate?: boolean;
    style?: any;
  };
}

export function generateRoutes(
  nlpEngine: NLPEngine,
  generatorFactory: GeneratorFactory,
  templateManager: TemplateManager
): Router {
  const router = Router();

  /**
   * POST /api/generate
   * Generate a single diagram from description
   */
  router.post('/', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const startTime = Date.now();
      const { description, format = OutputFormat.MERMAID, template, options = {} }: GenerateRequest = req.body;

      // Validate request
      if (!description || typeof description !== 'string' || description.trim().length === 0) {
        return res.status(400).json({
          error: 'Description is required and must be a non-empty string',
          code: 'INVALID_DESCRIPTION',
        });
      }

      if (description.length > 10000) {
        return res.status(400).json({
          error: 'Description is too long (maximum 10,000 characters)',
          code: 'DESCRIPTION_TOO_LONG',
        });
      }

      logger.info(`Generating diagram: format=${format}, template=${template || 'none'}`);

      // Parse the description
      const parseResult = await nlpEngine.parse(description);
      
      if (parseResult.confidence < 0.3) {
        return res.status(400).json({
          error: 'Unable to understand the description. Please provide a clearer description.',
          code: 'LOW_CONFIDENCE',
          confidence: parseResult.confidence,
          suggestions: [
            'Include specific component types (e.g., "API gateway", "database", "service")',
            'Describe relationships between components',
            'Use common architecture terminology',
          ],
        });
      }

      let architecture = parseResult.architecture;

      // Apply template if specified
      if (template) {
        try {
          const templateObj = await templateManager.getTemplate(template);
          architecture = await templateManager.applyTemplate(templateObj, architecture);
        } catch (error) {
          return res.status(404).json({
            error: `Template not found: ${template}`,
            code: 'TEMPLATE_NOT_FOUND',
          });
        }
      }

      // Generate the diagram
      const generator = generatorFactory.createGenerator(format);
      const generatorOptions = {
        style: options.style,
        features: {
          includeMetadata: true,
          includeComments: true,
          includeValidation: options.validate ?? true,
          ...options.features,
        },
      };

      const result = await generator.generate(architecture, generatorOptions);

      // Validate if requested
      let validation = null;
      if (options.validate) {
        validation = await generator.validate(architecture);
      }

      const processingTime = Date.now() - startTime;

      res.json({
        success: true,
        data: {
          content: result.content,
          format,
          architecture: {
            id: architecture.id,
            name: architecture.name,
            description: architecture.description,
            componentCount: architecture.components.length,
            connectionCount: architecture.connections.length,
            pattern: parseResult.suggestedPattern,
          },
          metadata: {
            ...result.metadata,
            processingTime,
            confidence: parseResult.confidence,
            template: template || null,
          },
          validation,
          warnings: result.warnings,
          statistics: result.statistics,
        },
      });

      logger.info(`Diagram generated successfully in ${processingTime}ms`);

    } catch (error) {
      logger.error('Generation failed:', error);
      next(error);
    }
  });

  /**
   * POST /api/generate/batch
   * Generate multiple diagrams from descriptions
   */
  router.post('/batch', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const startTime = Date.now();
      const { descriptions, format = OutputFormat.MERMAID, template, options = {} }: BatchGenerateRequest = req.body;

      // Validate request
      if (!Array.isArray(descriptions) || descriptions.length === 0) {
        return res.status(400).json({
          error: 'Descriptions must be a non-empty array',
          code: 'INVALID_DESCRIPTIONS',
        });
      }

      if (descriptions.length > 50) {
        return res.status(400).json({
          error: 'Too many descriptions (maximum 50 per batch)',
          code: 'TOO_MANY_DESCRIPTIONS',
        });
      }

      const parallel = Math.min(options.parallel || 4, 10);
      logger.info(`Batch generating ${descriptions.length} diagrams with ${parallel} parallel workers`);

      const results = [];
      const errors = [];

      // Process in parallel batches
      for (let i = 0; i < descriptions.length; i += parallel) {
        const batch = descriptions.slice(i, i + parallel);
        const batchPromises = batch.map(async (description, index) => {
          try {
            const globalIndex = i + index;
            
            // Parse description
            const parseResult = await nlpEngine.parse(description);
            let architecture = parseResult.architecture;

            // Apply template if specified
            if (template) {
              const templateObj = await templateManager.getTemplate(template);
              architecture = await templateManager.applyTemplate(templateObj, architecture);
            }

            // Generate diagram
            const generator = generatorFactory.createGenerator(format);
            const result = await generator.generate(architecture, {
              features: {
                includeMetadata: false,
                includeComments: false,
                includeValidation: options.validate ?? false,
              },
            });

            return {
              index: globalIndex,
              success: true,
              content: result.content,
              architecture: {
                name: architecture.name,
                componentCount: architecture.components.length,
                connectionCount: architecture.connections.length,
              },
              confidence: parseResult.confidence,
              warnings: result.warnings,
            };

          } catch (error) {
            return {
              index: i + index,
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        
        batchResults.forEach(result => {
          if (result.success) {
            results.push(result);
          } else {
            errors.push(result);
          }
        });
      }

      const processingTime = Date.now() - startTime;

      res.json({
        success: true,
        data: {
          results,
          errors,
          summary: {
            total: descriptions.length,
            successful: results.length,
            failed: errors.length,
            format,
            template: template || null,
            processingTime,
          },
        },
      });

      logger.info(`Batch generation completed: ${results.length}/${descriptions.length} successful in ${processingTime}ms`);

    } catch (error) {
      logger.error('Batch generation failed:', error);
      next(error);
    }
  });

  /**
   * GET /api/generate/formats
   * Get supported output formats
   */
  router.get('/formats', (req: Request, res: Response) => {
    try {
      const formats = generatorFactory.getSupportedFormats();
      const formatInfo = generatorFactory.getAllGeneratorInfo();

      res.json({
        success: true,
        data: {
          formats,
          generators: formatInfo.map(info => ({
            format: info.format,
            name: info.name,
            version: info.version,
            description: info.description,
            supported: true,
          })),
        },
      });

    } catch (error) {
      logger.error('Failed to get formats:', error);
      res.status(500).json({
        error: 'Failed to retrieve supported formats',
        code: 'FORMATS_ERROR',
      });
    }
  });

  /**
   * POST /api/generate/validate
   * Validate a description without generating
   */
  router.post('/validate', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { description }: { description: string } = req.body;

      if (!description || typeof description !== 'string') {
        return res.status(400).json({
          error: 'Description is required',
          code: 'INVALID_DESCRIPTION',
        });
      }

      const parseResult = await nlpEngine.parse(description);

      res.json({
        success: true,
        data: {
          confidence: parseResult.confidence,
          suggestedPattern: parseResult.suggestedPattern,
          components: parseResult.extractedComponents.map(c => ({
            name: c.name,
            type: c.type,
            confidence: c.confidence,
          })),
          connections: parseResult.extractedConnections.map(c => ({
            source: c.source,
            target: c.target,
            type: c.type,
            confidence: c.confidence,
          })),
          recommendations: generateRecommendations(parseResult),
        },
      });

    } catch (error) {
      logger.error('Validation failed:', error);
      next(error);
    }
  });

  /**
   * GET /api/generate/examples
   * Get example descriptions and their expected outputs
   */
  router.get('/examples', (req: Request, res: Response) => {
    const examples = [
      {
        id: 'microservices-basic',
        name: 'Basic Microservices',
        description: 'microservices architecture with API gateway, user service, and order service connected to databases',
        expectedComponents: ['API Gateway', 'User Service', 'Order Service', 'User Database', 'Order Database'],
        pattern: 'microservices',
        complexity: 'moderate',
      },
      {
        id: 'serverless-simple',
        name: 'Simple Serverless',
        description: 'serverless architecture with lambda functions, API gateway, and DynamoDB database',
        expectedComponents: ['API Gateway', 'Lambda Functions', 'DynamoDB'],
        pattern: 'serverless',
        complexity: 'simple',
      },
      {
        id: 'event-driven',
        name: 'Event-Driven Architecture',
        description: 'event-driven system with message queue, event processor, and notification service',
        expectedComponents: ['Message Queue', 'Event Processor', 'Notification Service'],
        pattern: 'event_driven',
        complexity: 'moderate',
      },
      {
        id: 'monolithic',
        name: 'Monolithic Application',
        description: 'monolithic web application with load balancer, application server, and database',
        expectedComponents: ['Load Balancer', 'Application Server', 'Database'],
        pattern: 'monolithic',
        complexity: 'simple',
      },
    ];

    res.json({
      success: true,
      data: {
        examples,
        usage: {
          description: 'Use these examples to understand how to describe architectures',
          tips: [
            'Be specific about component types',
            'Describe relationships between components',
            'Use common architecture patterns',
            'Include data flow information',
          ],
        },
      },
    });
  });

  return router;
}

/**
 * Generate recommendations based on parse result
 */
function generateRecommendations(parseResult: any): string[] {
  const recommendations: string[] = [];

  if (parseResult.confidence < 0.5) {
    recommendations.push('Consider adding more specific component types (e.g., "REST API", "PostgreSQL database")');
  }

  if (parseResult.extractedComponents.length < 2) {
    recommendations.push('Add more components to create a meaningful architecture');
  }

  if (parseResult.extractedConnections.length === 0) {
    recommendations.push('Describe how components interact with each other');
  }

  const hasDatabase = parseResult.extractedComponents.some((c: any) => c.type.includes('database'));
  const hasService = parseResult.extractedComponents.some((c: any) => c.type.includes('service'));
  
  if (hasService && !hasDatabase) {
    recommendations.push('Consider adding data storage components');
  }

  if (parseResult.extractedComponents.length > 5 && !parseResult.extractedComponents.some((c: any) => c.type.includes('gateway'))) {
    recommendations.push('For complex architectures, consider adding an API gateway');
  }

  return recommendations;
}
